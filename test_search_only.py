"""
Test script to verify the improved search functionality
"""

import asyncio
import os
from facebook_scraper import FacebookScraper


async def test_search_functionality():
    """Test only the search functionality without full analysis"""
    
    # Get Gemini API key
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ Please set GEMINI_API_KEY environment variable")
        return
    
    print("🧪 Testing Facebook Search Functionality")
    print("=" * 50)
    
    # Get search parameters
    last_name = input("Enter last name to test search (e.g., <PERSON>): ").strip()
    if not last_name:
        last_name = "<PERSON>"
    
    try:
        async with FacebookScraper(gemini_api_key=gemini_api_key, headless=False) as scraper:
            print("✅ Browser started successfully")
            
            # Test manual login
            print("🔐 Please login to Facebook manually...")
            login_success = await scraper.login_to_facebook_manual(timeout=300)
            
            if not login_success:
                print("❌ Login failed!")
                return
            
            print("✅ Login successful!")
            
            # Test search functionality
            print(f"\n🔍 Testing search for last name: {last_name}")
            users = await scraper.search_users_by_last_name(last_name, max_results=5)
            
            if users:
                print(f"\n✅ Search successful! Found {len(users)} users:")
                for i, user in enumerate(users, 1):
                    print(f"   {i}. {user.name}")
                    print(f"      URL: {user.profile_url}")
                
                # Test visiting one profile (without scraping posts)
                if users:
                    test_user = users[0]
                    print(f"\n🧪 Testing profile visit for: {test_user.name}")
                    
                    # Just test navigation without full post scraping
                    profile_url = await scraper._get_direct_profile_url(test_user)
                    print(f"📱 Navigating to: {profile_url}")
                    
                    await scraper.tab.go_to(profile_url)
                    await asyncio.sleep(5)
                    
                    current_url = await scraper.tab.current_url
                    is_valid = await scraper._verify_profile_page(current_url, test_user.name)
                    
                    if is_valid:
                        print("✅ Successfully reached profile page")
                    else:
                        print("❌ Failed to reach correct profile page")
                        print(f"   Current URL: {current_url}")
                
            else:
                print("❌ No users found in search")
                
                # Check what page we're on
                current_url = await scraper.tab.current_url
                print(f"Current URL: {current_url}")
                
                if "search" not in current_url:
                    print("⚠️  Not on search results page - search may have failed")
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("Facebook Search Test")
    print("=" * 30)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded from .env")
    except ImportError:
        print("ℹ️  python-dotenv not installed, using system environment variables")
    
    asyncio.run(test_search_functionality())
