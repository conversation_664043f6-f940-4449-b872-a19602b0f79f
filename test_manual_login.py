"""
Simple test script to verify manual login functionality
"""

import asyncio
import os
from facebook_scraper import FacebookScraper


async def test_manual_login():
    """Test the improved manual login functionality"""

    # Get Gemini API key
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ Please set GEMINI_API_KEY environment variable")
        return

    print("🧪 Testing Facebook Intelligent Manual Login")
    print("=" * 50)

    try:
        async with FacebookScraper(gemini_api_key=gemini_api_key, headless=False) as scraper:
            print("✅ Browser started successfully")

            # Test manual login with reasonable timeout
            print("🔐 Testing intelligent manual login...")
            print("This will open Facebook login page and intelligently detect login status")
            print("Handle any 2FA, captcha, or security checks as needed")

            login_success = await scraper.login_to_facebook_manual(timeout=300)  # 5 minutes

            if login_success:
                print("✅ Manual login successful!")

                # Test getting current URL
                current_url = await scraper.tab.current_url
                print(f"📍 Current URL: {current_url}")

                # Test basic search functionality
                print("🧪 Testing basic search...")
                test_users = await scraper.search_users_by_last_name("Smith", max_results=3)
                print(f"📊 Found {len(test_users)} test users")

                for user in test_users:
                    print(f"   👤 {user.name} - {user.profile_url}")

            else:
                print("❌ Manual login failed or timed out")

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("Facebook Tour Scraper - Manual Login Test")
    print("=" * 50)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded from .env")
    except ImportError:
        print("ℹ️  python-dotenv not installed, using system environment variables")
    
    asyncio.run(test_manual_login())
