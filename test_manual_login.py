"""
Simple test script to verify manual login functionality
"""

import asyncio
import os
from facebook_scraper import FacebookScraper


async def test_manual_login():
    """Test the manual login functionality"""
    
    # Get Gemini API key
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ Please set GEMINI_API_KEY environment variable")
        return
    
    print("🧪 Testing Facebook Manual Login")
    print("=" * 40)
    
    try:
        async with FacebookScraper(gemini_api_key=gemini_api_key, headless=False) as scraper:
            print("✅ Browser started successfully")
            
            # Test manual login with short timeout for testing
            print("🔐 Testing manual login (30 second timeout)...")
            print("Note: This will open Facebook login page")
            print("You can close the browser to test timeout handling")
            
            login_success = await scraper.login_to_facebook_manual(timeout=30)
            
            if login_success:
                print("✅ Manual login successful!")
                
                # Test getting current URL
                current_url = await scraper.tab.current_url
                print(f"📍 Current URL: {current_url}")
                
                # Test basic navigation
                print("🧪 Testing basic navigation...")
                await scraper.tab.go_to("https://www.facebook.com")
                await asyncio.sleep(2)
                
                new_url = await scraper.tab.current_url
                print(f"📍 After navigation: {new_url}")
                
            else:
                print("❌ Manual login failed or timed out")
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("Facebook Tour Scraper - Manual Login Test")
    print("=" * 50)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded from .env")
    except ImportError:
        print("ℹ️  python-dotenv not installed, using system environment variables")
    
    asyncio.run(test_manual_login())
