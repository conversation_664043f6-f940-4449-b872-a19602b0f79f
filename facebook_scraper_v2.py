"""
Facebook Scraper V2 - Direct profile input + Screenshot analysis
"""

import asyncio
import base64
import json
import os
import time
from google import genai
from google.genai import types
from pydoll.browser import Chrome
from pydoll.browser.options import ChromiumOptions


class FacebookScraperV2:
    """Facebook scraper with direct profile input and screenshot analysis"""
    
    def __init__(self, gemini_api_key: str):
        self.gemini_api_key = gemini_api_key
        self.browser = None
        self.tab = None
        self.logged_in = False
        
        # Initialize Gemini
        self.gemini_client = genai.Client(api_key=gemini_api_key)
        self.gemini_model = "gemini-2.5-flash-preview-04-17"
    
    async def __aenter__(self):
        await self.start_browser()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close_browser()
    
    async def start_browser(self):
        """Start the browser"""
        options = ChromiumOptions()
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--window-size=1920,1080')
        
        self.browser = Chrome(options=options)
        self.tab = await self.browser.start(headless=False)
        print("✅ Browser started")
    
    async def close_browser(self):
        """Close the browser"""
        if self.browser:
            try:
                await self.browser.stop()
                print("✅ Browser closed")
            except:
                pass
    
    async def manual_login(self, timeout: int = 600) -> bool:
        """Manual login to Facebook"""
        try:
            print("🔐 Opening Facebook login page...")
            await self.tab.go_to("https://www.facebook.com/login")
            
            print("\n" + "="*60)
            print("🔐 MANUAL LOGIN REQUIRED")
            print("="*60)
            print("Please login to Facebook manually in the browser.")
            print("The scraper will detect when you're logged in.")
            print("="*60)
            
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    current_url = await self.tab.current_url
                    
                    # Check if logged in
                    if ("facebook.com" in current_url and 
                        "login" not in current_url and 
                        "checkpoint" not in current_url):
                        
                        # Additional verification
                        try:
                            page_title = await self.tab.execute_script("return document.title;")
                            if "Facebook" in page_title and "Log In" not in page_title:
                                self.logged_in = True
                                print("\n✅ Login detected successfully!")
                                return True
                        except:
                            pass
                    
                    # Show progress every 10 seconds
                    elapsed = int(time.time() - start_time)
                    if elapsed % 10 == 0:
                        remaining = timeout - elapsed
                        print(f"\r⏳ Waiting for login... {elapsed}s elapsed, {remaining}s remaining", end="", flush=True)
                    
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    await asyncio.sleep(2)
            
            print(f"\n❌ Login timeout after {timeout} seconds")
            return False
            
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    async def visit_profile_directly(self, profile_url: str) -> bool:
        """Visit a Facebook profile directly"""
        try:
            if not self.logged_in:
                print("❌ Must be logged in first")
                return False
            
            print(f"📱 Visiting profile: {profile_url}")
            await self.tab.go_to(profile_url)
            await asyncio.sleep(5)
            
            # Check if we reached the profile
            current_url = await self.tab.current_url
            if "facebook.com" in current_url and "login" not in current_url:
                print("✅ Profile loaded successfully")
                return True
            else:
                print(f"❌ Failed to load profile. Current URL: {current_url}")
                return False
                
        except Exception as e:
            print(f"❌ Error visiting profile: {e}")
            return False
    
    async def take_screenshot(self) -> bytes:
        """Take a screenshot of the current page"""
        try:
            # Take screenshot using pyDoll
            screenshot_data = await self.tab.execute_script("""
                return new Promise((resolve) => {
                    html2canvas(document.body).then(canvas => {
                        resolve(canvas.toDataURL('image/png').split(',')[1]);
                    });
                });
            """)
            
            if screenshot_data:
                return base64.b64decode(screenshot_data)
            else:
                # Fallback method
                screenshot_b64 = await self.tab.execute_script("""
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = window.innerWidth;
                    canvas.height = window.innerHeight;
                    
                    // Simple screenshot simulation
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    ctx.fillStyle = '#000000';
                    ctx.font = '16px Arial';
                    ctx.fillText('Page Content: ' + document.body.innerText.substring(0, 100), 10, 30);
                    
                    return canvas.toDataURL('image/png').split(',')[1];
                """)
                
                return base64.b64decode(screenshot_b64)
                
        except Exception as e:
            print(f"❌ Screenshot error: {e}")
            # Create a simple text-based "screenshot"
            try:
                page_text = await self.tab.execute_script("return document.body.innerText;")
                # Convert text to a simple image representation
                import io
                from PIL import Image, ImageDraw, ImageFont
                
                # Create a simple text image
                img = Image.new('RGB', (800, 600), color='white')
                draw = ImageDraw.Draw(img)
                
                # Wrap text
                lines = []
                words = page_text[:1000].split()
                line = ""
                for word in words:
                    if len(line + word) < 80:
                        line += word + " "
                    else:
                        lines.append(line)
                        line = word + " "
                        if len(lines) > 30:  # Limit lines
                            break
                
                # Draw text
                y = 10
                for line in lines:
                    draw.text((10, y), line, fill='black')
                    y += 20
                
                # Convert to bytes
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='PNG')
                return img_bytes.getvalue()
                
            except:
                return b""  # Empty bytes if all fails
    
    async def analyze_screenshot_with_gemini(self, screenshot_data: bytes, criteria: str) -> dict:
        """Analyze screenshot with Gemini AI"""
        try:
            if not screenshot_data:
                return {
                    "matches": False,
                    "confidence": 0.0,
                    "analysis": "No screenshot data available"
                }
            
            prompt = f"""
            Analyze this Facebook profile screenshot for content matching: "{criteria}"
            
            Look for:
            - Posts about the specified criteria
            - Images related to the criteria
            - Any text content that matches
            - Profile information relevant to the criteria
            
            Respond with JSON:
            {{
                "matches": true/false,
                "confidence": 0.0-1.0,
                "findings": ["list", "of", "specific", "findings"],
                "analysis": "detailed explanation of what you found"
            }}
            """
            
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_bytes(
                            mime_type="image/png",
                            data=screenshot_data
                        ),
                        types.Part.from_text(text=prompt)
                    ]
                )
            ]
            
            config = types.GenerateContentConfig(
                response_mime_type="application/json",
            )
            
            response = await asyncio.to_thread(
                self.gemini_client.models.generate_content,
                model=self.gemini_model,
                contents=contents,
                config=config
            )
            
            return json.loads(response.text)
            
        except Exception as e:
            print(f"❌ Gemini screenshot analysis error: {e}")
            return {
                "matches": False,
                "confidence": 0.0,
                "findings": [],
                "analysis": f"Analysis failed: {e}"
            }
    
    async def scrape_posts_text_and_images(self) -> dict:
        """Scrape both text and images from current profile page"""
        try:
            print("📜 Scrolling to load more content...")
            
            # Scroll to load more posts
            for i in range(5):
                await self.tab.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                await asyncio.sleep(2)
                print(f"   Scroll {i+1}/5 completed")
            
            # Get all text content
            page_text = await self.tab.execute_script("return document.body.innerText;")
            
            # Get all images
            images = []
            try:
                img_elements = await self.tab.find(tag_name="img", find_all=True, timeout=10)
                for img in img_elements:
                    try:
                        src = img.get_attribute("src")
                        alt = img.get_attribute("alt") or ""
                        if src and ("scontent" in src or "fbcdn" in src):  # Facebook images
                            images.append({
                                "url": src,
                                "alt": alt
                            })
                    except:
                        continue
            except:
                pass
            
            print(f"📊 Found {len(images)} images and {len(page_text)} characters of text")
            
            return {
                "text_content": page_text,
                "images": images,
                "timestamp": int(time.time())
            }
            
        except Exception as e:
            print(f"❌ Error scraping content: {e}")
            return {
                "text_content": "",
                "images": [],
                "timestamp": int(time.time()),
                "error": str(e)
            }
    
    async def analyze_scraped_content(self, content: dict, criteria: str) -> dict:
        """Analyze scraped text and images with Gemini"""
        try:
            # Analyze text content
            text_analysis = await self.analyze_text_with_gemini(content["text_content"], criteria)
            
            # Analyze images (if any)
            image_analyses = []
            for img in content["images"][:5]:  # Limit to first 5 images
                try:
                    # Download and analyze image
                    img_data = await self.download_image(img["url"])
                    if img_data:
                        img_analysis = await self.analyze_image_with_gemini(img_data, criteria)
                        image_analyses.append(img_analysis)
                except:
                    continue
            
            # Combine analyses
            combined_analysis = {
                "text_analysis": text_analysis,
                "image_analyses": image_analyses,
                "overall_match": text_analysis.get("matches", False) or any(img.get("matches", False) for img in image_analyses),
                "combined_confidence": max([text_analysis.get("confidence", 0.0)] + [img.get("confidence", 0.0) for img in image_analyses]),
                "summary": f"Text: {text_analysis.get('analysis', 'No analysis')}. Images: {len(image_analyses)} analyzed."
            }
            
            return combined_analysis
            
        except Exception as e:
            print(f"❌ Error analyzing content: {e}")
            return {
                "error": str(e),
                "overall_match": False,
                "combined_confidence": 0.0
            }
    
    async def analyze_text_with_gemini(self, text: str, criteria: str) -> dict:
        """Analyze text with Gemini"""
        try:
            prompt = f"""
            Analyze this Facebook profile text for content matching: "{criteria}"
            
            Text: "{text[:2000]}"  # Limit text length
            
            Respond with JSON:
            {{
                "matches": true/false,
                "confidence": 0.0-1.0,
                "keywords": ["relevant", "keywords", "found"],
                "analysis": "explanation"
            }}
            """
            
            contents = [types.Content(role="user", parts=[types.Part.from_text(text=prompt)])]
            config = types.GenerateContentConfig(response_mime_type="application/json")
            
            response = await asyncio.to_thread(
                self.gemini_client.models.generate_content,
                model=self.gemini_model,
                contents=contents,
                config=config
            )
            
            return json.loads(response.text)
            
        except Exception as e:
            return {"matches": False, "confidence": 0.0, "analysis": f"Error: {e}"}
    
    async def analyze_image_with_gemini(self, image_data: bytes, criteria: str) -> dict:
        """Analyze image with Gemini"""
        try:
            prompt = f"""
            Analyze this image for content matching: "{criteria}"
            
            Respond with JSON:
            {{
                "matches": true/false,
                "confidence": 0.0-1.0,
                "description": "what you see in the image",
                "analysis": "how it relates to the criteria"
            }}
            """
            
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_bytes(mime_type="image/jpeg", data=image_data),
                        types.Part.from_text(text=prompt)
                    ]
                )
            ]
            
            config = types.GenerateContentConfig(response_mime_type="application/json")
            
            response = await asyncio.to_thread(
                self.gemini_client.models.generate_content,
                model=self.gemini_model,
                contents=contents,
                config=config
            )
            
            return json.loads(response.text)
            
        except Exception as e:
            return {"matches": False, "confidence": 0.0, "analysis": f"Error: {e}"}
    
    async def download_image(self, image_url: str) -> bytes:
        """Download image from URL"""
        try:
            # Use browser to download image
            response = await self.tab.execute_script(f"""
                return fetch('{image_url}')
                    .then(response => response.arrayBuffer())
                    .then(buffer => Array.from(new Uint8Array(buffer)));
            """)
            
            if response and isinstance(response, list):
                return bytes(response)
            
        except Exception as e:
            print(f"Error downloading image: {e}")
        
        return b""


async def main():
    """Main function with multiple analysis options"""
    # Get API key
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ Please set GEMINI_API_KEY environment variable")
        return

    print("🚀 Facebook Scraper V2 - Direct Profile Analysis")
    print("=" * 60)

    # Get analysis method
    print("Choose analysis method:")
    print("1. Direct profile URLs (you provide the URLs)")
    print("2. Search for profiles first")
    print("3. Screenshot analysis only")

    method = input("Choose method (1/2/3): ").strip()

    # Get criteria
    criteria = input("What to look for: ").strip()
    if not criteria:
        criteria = "travel posts"

    async with FacebookScraperV2(gemini_api_key) as scraper:
        # Login
        login_success = await scraper.manual_login()
        if not login_success:
            print("❌ Login failed!")
            return

        profiles_to_analyze = []

        if method == "1":
            # Direct profile URLs
            print("\nEnter Facebook profile URLs (one per line, empty line to finish):")
            while True:
                url = input("Profile URL: ").strip()
                if not url:
                    break
                profiles_to_analyze.append(url)

        elif method == "2":
            # Search first
            search_string = input("Search string: ").strip()
            if search_string:
                # Use simple search
                await scraper.tab.go_to("https://www.facebook.com")
                await asyncio.sleep(3)

                # Find search box and search
                search_input = await scraper.tab.find(tag_name="input", placeholder="Search Facebook", timeout=5, raise_exc=False)
                if search_input:
                    await search_input.click()
                    await search_input.type_text(search_string, interval=0.1)
                    await asyncio.sleep(2)

                    # Press Enter
                    await scraper.tab.execute_script("""
                        var event = new KeyboardEvent('keydown', {
                            key: 'Enter',
                            keyCode: 13,
                            bubbles: true
                        });
                        arguments[0].dispatchEvent(event);
                    """, search_input)

                    await asyncio.sleep(5)
                    print("✅ Search completed. Please manually copy profile URLs from the results.")

                    # Let user manually select profiles
                    while True:
                        url = input("Enter profile URL from search results (empty to finish): ").strip()
                        if not url:
                            break
                        profiles_to_analyze.append(url)

        elif method == "3":
            # Screenshot analysis only
            profile_url = input("Enter profile URL for screenshot analysis: ").strip()
            if profile_url:
                success = await scraper.visit_profile_directly(profile_url)
                if success:
                    print("📸 Taking screenshot...")
                    screenshot = await scraper.take_screenshot()

                    print("🤖 Analyzing screenshot with Gemini...")
                    analysis = await scraper.analyze_screenshot_with_gemini(screenshot, criteria)

                    print(f"\n📊 Screenshot Analysis Results:")
                    print(f"   Matches: {analysis.get('matches', False)}")
                    print(f"   Confidence: {analysis.get('confidence', 0.0):.2f}")
                    print(f"   Analysis: {analysis.get('analysis', 'No analysis')}")

                    # Save results
                    timestamp = int(time.time())
                    output_file = f"screenshot_analysis_{timestamp}.json"

                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            'method': 'screenshot',
                            'profile_url': profile_url,
                            'criteria': criteria,
                            'analysis': analysis,
                            'timestamp': timestamp
                        }, f, indent=2, ensure_ascii=False)

                    print(f"💾 Results saved to: {output_file}")
            return

        # Analyze profiles
        if not profiles_to_analyze:
            print("❌ No profiles to analyze!")
            return

        print(f"\n🎯 Analyzing {len(profiles_to_analyze)} profiles for: '{criteria}'")

        all_results = []

        for i, profile_url in enumerate(profiles_to_analyze, 1):
            print(f"\n👤 Profile {i}/{len(profiles_to_analyze)}: {profile_url}")

            # Visit profile
            success = await scraper.visit_profile_directly(profile_url)
            if not success:
                print("❌ Failed to load profile")
                continue

            # Choose analysis method
            print("Choose analysis for this profile:")
            print("1. Screenshot analysis (fast)")
            print("2. Text + Image scraping (detailed)")
            print("3. Both methods")

            analysis_choice = input("Choice (1/2/3): ").strip() or "1"

            profile_results = {
                "profile_url": profile_url,
                "criteria": criteria,
                "timestamp": int(time.time())
            }

            if analysis_choice in ["1", "3"]:
                # Screenshot analysis
                print("📸 Taking screenshot...")
                screenshot = await scraper.take_screenshot()

                print("🤖 Analyzing screenshot...")
                screenshot_analysis = await scraper.analyze_screenshot_with_gemini(screenshot, criteria)
                profile_results["screenshot_analysis"] = screenshot_analysis

                print(f"   Screenshot - Matches: {screenshot_analysis.get('matches', False)}, Confidence: {screenshot_analysis.get('confidence', 0.0):.2f}")

            if analysis_choice in ["2", "3"]:
                # Text + Image scraping
                print("📜 Scraping content...")
                scraped_content = await scraper.scrape_posts_text_and_images()

                print("🤖 Analyzing scraped content...")
                content_analysis = await scraper.analyze_scraped_content(scraped_content, criteria)
                profile_results["content_analysis"] = content_analysis
                profile_results["scraped_content"] = scraped_content

                print(f"   Content - Matches: {content_analysis.get('overall_match', False)}, Confidence: {content_analysis.get('combined_confidence', 0.0):.2f}")

            all_results.append(profile_results)

            # Ask if user wants to continue
            if i < len(profiles_to_analyze):
                continue_choice = input("Continue to next profile? (y/n): ").strip().lower()
                if continue_choice == 'n':
                    break

        # Save all results
        timestamp = int(time.time())
        output_file = f"facebook_analysis_results_{timestamp}.json"

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'method': method,
                'criteria': criteria,
                'profiles_analyzed': len(all_results),
                'results': all_results,
                'timestamp': timestamp
            }, f, indent=2, ensure_ascii=False)

        print(f"\n💾 All results saved to: {output_file}")

        # Summary
        matching_profiles = []
        for result in all_results:
            screenshot_match = result.get("screenshot_analysis", {}).get("matches", False)
            content_match = result.get("content_analysis", {}).get("overall_match", False)

            if screenshot_match or content_match:
                matching_profiles.append(result["profile_url"])

        print(f"\n📊 SUMMARY:")
        print(f"   Profiles analyzed: {len(all_results)}")
        print(f"   Matching profiles: {len(matching_profiles)}")
        print(f"   Criteria: '{criteria}'")

        if matching_profiles:
            print(f"\n✅ Matching profiles:")
            for url in matching_profiles:
                print(f"   - {url}")

        print("🎉 Analysis complete!")


if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass

    asyncio.run(main())
