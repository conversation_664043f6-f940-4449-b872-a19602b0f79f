"""
Facebook Scraper V2 - Direct profile input + Screenshot analysis
"""

import asyncio
import base64
import json
import os
import time
from google import genai
from google.genai import types
from pydoll.browser import Chrome
from pydoll.browser.options import ChromiumOptions


class FacebookScraperV2:
    """Facebook scraper with direct profile input and screenshot analysis"""
    
    def __init__(self, gemini_api_key: str):
        self.gemini_api_key = gemini_api_key
        self.browser = None
        self.tab = None
        self.logged_in = False
        
        # Initialize Gemini
        self.gemini_client = genai.Client(api_key=gemini_api_key)
        self.gemini_model = "gemini-2.5-flash-preview-04-17"
    
    async def __aenter__(self):
        await self.start_browser()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close_browser()
    
    async def start_browser(self):
        """Start the browser"""
        options = ChromiumOptions()
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--window-size=1920,1080')
        
        self.browser = Chrome(options=options)
        self.tab = await self.browser.start(headless=False)
        print("✅ Browser started")
    
    async def close_browser(self):
        """Close the browser"""
        if self.browser:
            try:
                await self.browser.stop()
                print("✅ Browser closed")
            except:
                pass
    
    async def manual_login(self, timeout: int = 600) -> bool:
        """Intelligent manual login to Facebook"""
        try:
            print("🔐 Opening Facebook login page...")
            await self.tab.go_to("https://www.facebook.com/login")

            # Handle Cloudflare protection if present
            try:
                async with self.tab.expect_and_bypass_cloudflare_captcha():
                    pass
            except:
                pass

            print("\n" + "="*60)
            print("🔐 MANUAL LOGIN REQUIRED")
            print("="*60)
            print("Please login to Facebook manually in the browser.")
            print("The scraper will intelligently detect when you're logged in.")
            print("Handle any 2FA, captcha, or security checks as needed.")
            print(f"Timeout: {timeout} seconds ({timeout//60} minutes)")
            print("="*60)

            # Intelligent login detection
            start_time = time.time()
            last_url = ""
            consecutive_same_url = 0

            while time.time() - start_time < timeout:
                try:
                    # Get current URL and check for login status
                    current_url = await self.tab.current_url

                    # Track URL changes to detect navigation
                    if current_url == last_url:
                        consecutive_same_url += 1
                    else:
                        consecutive_same_url = 0
                        last_url = current_url

                    # Multiple checks for successful login
                    login_indicators = await self._check_login_status(current_url)

                    if login_indicators["is_logged_in"]:
                        self.logged_in = True
                        print(f"\n✅ Login detected successfully!")
                        print(f"   Status: {login_indicators['status']}")
                        return True

                    # Handle special cases
                    if login_indicators["needs_attention"]:
                        print(f"\n⚠️  {login_indicators['message']}")
                        print("   Please complete the required action in the browser.")

                    # Show progress (less frequent updates)
                    elapsed = int(time.time() - start_time)
                    remaining = timeout - elapsed

                    if elapsed % 10 == 0:  # Update every 10 seconds
                        status_msg = login_indicators.get('status', 'Waiting for login')
                        print(f"\r⏳ {status_msg}... {elapsed}s elapsed, {remaining}s remaining", end="", flush=True)

                    # Adaptive check interval based on activity
                    if consecutive_same_url > 10:
                        check_interval = 3  # Slower checks if no activity
                    else:
                        check_interval = 1  # Faster checks during activity

                    await asyncio.sleep(check_interval)

                except Exception as e:
                    await asyncio.sleep(2)

            print(f"\n❌ Login timeout after {timeout} seconds")
            return False

        except Exception as e:
            print(f"❌ Login error: {e}")
            return False

    async def _check_login_status(self, current_url: str) -> dict:
        """Intelligently check if user is logged in to Facebook"""
        result = {
            "is_logged_in": False,
            "needs_attention": False,
            "status": "Checking login status",
            "message": ""
        }

        try:
            # URL-based checks
            if "login" in current_url:
                result["status"] = "On login page"
                return result

            if "checkpoint" in current_url:
                result["needs_attention"] = True
                result["status"] = "Security checkpoint"
                result["message"] = "Facebook security checkpoint detected. Please complete verification."
                return result

            if "two_factor" in current_url or "2fa" in current_url:
                result["needs_attention"] = True
                result["status"] = "Two-factor authentication"
                result["message"] = "Two-factor authentication required. Please enter your code."
                return result

            if "captcha" in current_url:
                result["needs_attention"] = True
                result["status"] = "Captcha verification"
                result["message"] = "Captcha verification required. Please complete the captcha."
                return result

            # If we're on facebook.com but not login/checkpoint pages
            if "facebook.com" in current_url:
                # Try multiple methods to confirm login
                login_confirmed = await self._confirm_facebook_login()

                if login_confirmed:
                    result["is_logged_in"] = True
                    result["status"] = "Successfully logged in"
                    return result
                else:
                    result["status"] = "Verifying login status"
                    return result

            result["status"] = "Navigating to Facebook"
            return result

        except Exception as e:
            result["status"] = "Error checking status"
            return result

    async def _confirm_facebook_login(self) -> bool:
        """Confirm that user is actually logged in using multiple checks"""
        try:
            # Method 1: Look for user profile menu/avatar
            profile_indicators = [
                {"tag_name": "div", "aria_label": "Account"},
                {"tag_name": "div", "aria_label": "Profile"},
                {"tag_name": "a", "aria_label": "Profile"},
                {"tag_name": "img", "alt": "Profile picture"},
            ]

            for indicator in profile_indicators:
                try:
                    element = await self.tab.find(timeout=2, raise_exc=False, **indicator)
                    if element:
                        return True
                except:
                    continue

            # Method 2: Look for navigation elements that only appear when logged in
            nav_indicators = ["Home", "Watch", "Marketplace", "Groups", "Gaming"]

            for nav_text in nav_indicators:
                try:
                    nav_element = await self.tab.find(
                        tag_name="a",
                        text=nav_text,
                        timeout=1,
                        raise_exc=False
                    )
                    if nav_element:
                        return True
                except:
                    continue

            # Method 3: Check for "What's on your mind" or posting interface
            posting_indicators = ["What's on your mind", "Write something", "Create post"]

            for post_text in posting_indicators:
                try:
                    post_element = await self.tab.find(
                        tag_name="div",
                        text=post_text,
                        timeout=1,
                        raise_exc=False
                    )
                    if post_element:
                        return True
                except:
                    continue

            # Method 4: Check page title
            try:
                title = await self.tab.execute_script("return document.title;")
                if title and "Facebook" in title and "Log In" not in title:
                    return True
            except:
                pass

            return False

        except Exception as e:
            return False

    async def search_users_by_exact_string(self, search_string: str, max_results: int = 20) -> list:
        """Search for Facebook users using exact string"""
        if not self.logged_in:
            print("❌ Must be logged in to search users")
            return []

        users = []

        try:
            print(f"🔍 Searching for exact string: '{search_string}'")

            # Go to Facebook main page first
            await self.tab.go_to("https://www.facebook.com")
            await asyncio.sleep(3)

            # Find and use the search box with exact string
            search_success = await self._perform_exact_facebook_search(search_string)

            if not search_success:
                print("❌ Search failed")
                return []

            # Wait for search results to load
            await asyncio.sleep(5)

            # Check if we're on a search results page
            current_url = await self.tab.current_url
            if "search" not in current_url:
                print("❌ Search failed - not on search results page")
                print(f"Current URL: {current_url}")
                return []

            print("✅ Search completed, looking for profile results...")

            # Scroll to load more results
            for scroll in range(3):
                await self.tab.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                await asyncio.sleep(2)
                print(f"   Scrolled {scroll + 1}/3 times to load more profiles")

            # Extract profile information from search results
            users = await self._extract_profiles_from_search_results(search_string, max_results)

            print(f"✅ Found {len(users)} profiles for '{search_string}'")

            return users

        except Exception as e:
            print(f"❌ Error during search: {e}")
            return users

    async def _perform_exact_facebook_search(self, search_string: str) -> bool:
        """Use Facebook's search box to search for the exact string"""
        try:
            # Look for search input field
            search_selectors = [
                {"tag_name": "input", "placeholder": "Search Facebook"},
                {"tag_name": "input", "aria_label": "Search Facebook"},
                {"tag_name": "input", "type": "search"},
                {"tag_name": "input", "name": "q"}
            ]

            search_input = None
            for selector in search_selectors:
                try:
                    search_input = await self.tab.find(timeout=3, raise_exc=False, **selector)
                    if search_input:
                        break
                except:
                    continue

            if not search_input:
                print("❌ Could not find search input field")
                return False

            # Click on search input and clear any existing text
            await search_input.click()
            await asyncio.sleep(1)

            # Clear the field first
            await self.tab.execute_script("""
                arguments[0].value = '';
                arguments[0].focus();
            """, search_input)

            # Type the exact search string
            print(f"   Typing: '{search_string}'")
            await search_input.type_text(search_string, interval=0.1)
            await asyncio.sleep(2)

            # Press Enter to search
            await self.tab.execute_script("""
                var event = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true
                });
                arguments[0].dispatchEvent(event);
            """, search_input)

            await asyncio.sleep(5)

            # Check if we're on search results
            current_url = await self.tab.current_url
            if "search" in current_url:
                print(f"   ✅ Search successful, on results page")
                return True
            else:
                print(f"   ❌ Search failed, current URL: {current_url}")
                return False

        except Exception as e:
            print(f"   ❌ Search error: {e}")
            return False

    async def _extract_profiles_from_search_results(self, search_string: str, max_results: int) -> list:
        """Extract profile information from Facebook search results page"""
        users = []

        try:
            print("🔍 Analyzing search results page...")

            # Get all links on the page
            all_links = await self.tab.find(tag_name="a", find_all=True, timeout=10)
            print(f"   Found {len(all_links)} total links")

            processed_urls = set()
            potential_profiles = []

            # Look through all links for profile URLs
            for link in all_links:
                try:
                    href = link.get_attribute("href")
                    if not href or href in processed_urls:
                        continue

                    processed_urls.add(href)

                    # Check if it's a valid profile URL
                    if self._is_valid_profile_url(href):
                        # Try to get the name/text associated with this link
                        link_text = await link.text

                        if link_text and link_text.strip():
                            # Clean up the text
                            cleaned_text = self._clean_extracted_name(link_text)

                            if cleaned_text:
                                potential_profiles.append({
                                    'name': cleaned_text,
                                    'url': href,
                                    'text': link_text
                                })

                except Exception as e:
                    continue

            print(f"   Found {len(potential_profiles)} potential profiles")

            # If we found profiles, create user objects
            if potential_profiles:
                for profile in potential_profiles[:max_results]:
                    users.append({
                        'name': profile['name'],
                        'profile_url': profile['url']
                    })
                    print(f"   👤 {profile['name']}")
                    print(f"      URL: {profile['url']}")

            return users[:max_results]

        except Exception as e:
            print(f"   ❌ Error extracting profiles: {e}")
            return users

    def _is_valid_profile_url(self, url: str) -> bool:
        """Check if URL is a Facebook profile"""
        if not url or "facebook.com" not in url:
            return False

        # Skip non-profile URLs
        skip_patterns = [
            "/pages/", "/groups/", "/events/", "/marketplace/",
            "/watch/", "/gaming/", "/search/", "/help/"
        ]

        if any(pattern in url for pattern in skip_patterns):
            return False

        # Valid profile patterns
        if "/profile.php?id=" in url:
            return True

        # Username profiles
        if "facebook.com/" in url:
            parts = url.split("facebook.com/")
            if len(parts) > 1:
                username = parts[1].split("/")[0].split("?")[0]
                if username and not username.startswith(("www", "m", "mobile")):
                    return True

        return False

    def _clean_extracted_name(self, text: str) -> str:
        """Clean and extract a proper name from link text"""
        if not text:
            return ""

        # Remove extra whitespace and newlines
        import re
        text = re.sub(r'\s+', ' ', text.strip())

        # Look for name pattern at the beginning
        name_match = re.match(r'^([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)', text)
        if name_match:
            return name_match.group(1)

        # If no pattern match, take first few words if they look like names
        words = text.split()
        if len(words) >= 2:
            potential_name = " ".join(words[:3])  # Take up to 3 words
            return potential_name

        return text[:50]  # Fallback to first 50 characters
    
    async def visit_profile_directly(self, profile_url: str) -> bool:
        """Visit a Facebook profile directly"""
        try:
            if not self.logged_in:
                print("❌ Must be logged in first")
                return False
            
            print(f"📱 Visiting profile: {profile_url}")
            await self.tab.go_to(profile_url)
            await asyncio.sleep(5)
            
            # Check if we reached the profile
            current_url = await self.tab.current_url
            if "facebook.com" in current_url and "login" not in current_url:
                print("✅ Profile loaded successfully")
                return True
            else:
                print(f"❌ Failed to load profile. Current URL: {current_url}")
                return False
                
        except Exception as e:
            print(f"❌ Error visiting profile: {e}")
            return False
    
    async def take_screenshot(self) -> bytes:
        """Take a screenshot of the current page"""
        try:
            # Take screenshot using pyDoll
            screenshot_data = await self.tab.execute_script("""
                return new Promise((resolve) => {
                    html2canvas(document.body).then(canvas => {
                        resolve(canvas.toDataURL('image/png').split(',')[1]);
                    });
                });
            """)
            
            if screenshot_data:
                return base64.b64decode(screenshot_data)
            else:
                # Fallback method
                screenshot_b64 = await self.tab.execute_script("""
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = window.innerWidth;
                    canvas.height = window.innerHeight;
                    
                    // Simple screenshot simulation
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    ctx.fillStyle = '#000000';
                    ctx.font = '16px Arial';
                    ctx.fillText('Page Content: ' + document.body.innerText.substring(0, 100), 10, 30);
                    
                    return canvas.toDataURL('image/png').split(',')[1];
                """)
                
                return base64.b64decode(screenshot_b64)
                
        except Exception as e:
            print(f"❌ Screenshot error: {e}")
            # Create a simple text-based "screenshot"
            try:
                page_text = await self.tab.execute_script("return document.body.innerText;")
                # Convert text to a simple image representation
                import io
                from PIL import Image, ImageDraw, ImageFont
                
                # Create a simple text image
                img = Image.new('RGB', (800, 600), color='white')
                draw = ImageDraw.Draw(img)
                
                # Wrap text
                lines = []
                words = page_text[:1000].split()
                line = ""
                for word in words:
                    if len(line + word) < 80:
                        line += word + " "
                    else:
                        lines.append(line)
                        line = word + " "
                        if len(lines) > 30:  # Limit lines
                            break
                
                # Draw text
                y = 10
                for line in lines:
                    draw.text((10, y), line, fill='black')
                    y += 20
                
                # Convert to bytes
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='PNG')
                return img_bytes.getvalue()
                
            except:
                return b""  # Empty bytes if all fails
    
    async def analyze_screenshot_with_gemini(self, screenshot_data: bytes, criteria: str) -> dict:
        """Analyze screenshot with Gemini AI"""
        try:
            if not screenshot_data:
                return {
                    "matches": False,
                    "confidence": 0.0,
                    "analysis": "No screenshot data available"
                }
            
            prompt = f"""
            Analyze this Facebook profile screenshot for content matching: "{criteria}"
            
            Look for:
            - Posts about the specified criteria
            - Images related to the criteria
            - Any text content that matches
            - Profile information relevant to the criteria
            
            Respond with JSON:
            {{
                "matches": true/false,
                "confidence": 0.0-1.0,
                "findings": ["list", "of", "specific", "findings"],
                "analysis": "detailed explanation of what you found"
            }}
            """
            
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_bytes(
                            mime_type="image/png",
                            data=screenshot_data
                        ),
                        types.Part.from_text(text=prompt)
                    ]
                )
            ]
            
            config = types.GenerateContentConfig(
                response_mime_type="application/json",
            )
            
            response = await asyncio.to_thread(
                self.gemini_client.models.generate_content,
                model=self.gemini_model,
                contents=contents,
                config=config
            )
            
            return json.loads(response.text)
            
        except Exception as e:
            print(f"❌ Gemini screenshot analysis error: {e}")
            return {
                "matches": False,
                "confidence": 0.0,
                "findings": [],
                "analysis": f"Analysis failed: {e}"
            }
    
    async def scrape_posts_text_and_images(self) -> dict:
        """Scrape both text and images from current profile page"""
        try:
            print("📜 Scrolling to load more content...")
            
            # Scroll to load more posts
            for i in range(5):
                await self.tab.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                await asyncio.sleep(2)
                print(f"   Scroll {i+1}/5 completed")
            
            # Get all text content
            page_text = await self.tab.execute_script("return document.body.innerText;")
            
            # Get all images
            images = []
            try:
                img_elements = await self.tab.find(tag_name="img", find_all=True, timeout=10)
                for img in img_elements:
                    try:
                        src = img.get_attribute("src")
                        alt = img.get_attribute("alt") or ""
                        if src and ("scontent" in src or "fbcdn" in src):  # Facebook images
                            images.append({
                                "url": src,
                                "alt": alt
                            })
                    except:
                        continue
            except:
                pass
            
            print(f"📊 Found {len(images)} images and {len(page_text)} characters of text")
            
            return {
                "text_content": page_text,
                "images": images,
                "timestamp": int(time.time())
            }
            
        except Exception as e:
            print(f"❌ Error scraping content: {e}")
            return {
                "text_content": "",
                "images": [],
                "timestamp": int(time.time()),
                "error": str(e)
            }
    
    async def analyze_scraped_content(self, content: dict, criteria: str) -> dict:
        """Analyze scraped text and images with Gemini"""
        try:
            # Analyze text content
            text_analysis = await self.analyze_text_with_gemini(content["text_content"], criteria)
            
            # Analyze images (if any)
            image_analyses = []
            for img in content["images"][:5]:  # Limit to first 5 images
                try:
                    # Download and analyze image
                    img_data = await self.download_image(img["url"])
                    if img_data:
                        img_analysis = await self.analyze_image_with_gemini(img_data, criteria)
                        image_analyses.append(img_analysis)
                except:
                    continue
            
            # Combine analyses
            combined_analysis = {
                "text_analysis": text_analysis,
                "image_analyses": image_analyses,
                "overall_match": text_analysis.get("matches", False) or any(img.get("matches", False) for img in image_analyses),
                "combined_confidence": max([text_analysis.get("confidence", 0.0)] + [img.get("confidence", 0.0) for img in image_analyses]),
                "summary": f"Text: {text_analysis.get('analysis', 'No analysis')}. Images: {len(image_analyses)} analyzed."
            }
            
            return combined_analysis
            
        except Exception as e:
            print(f"❌ Error analyzing content: {e}")
            return {
                "error": str(e),
                "overall_match": False,
                "combined_confidence": 0.0
            }
    
    async def analyze_text_with_gemini(self, text: str, criteria: str) -> dict:
        """Analyze text with Gemini"""
        try:
            prompt = f"""
            Analyze this Facebook profile text for content matching: "{criteria}"
            
            Text: "{text[:2000]}"  # Limit text length
            
            Respond with JSON:
            {{
                "matches": true/false,
                "confidence": 0.0-1.0,
                "keywords": ["relevant", "keywords", "found"],
                "analysis": "explanation"
            }}
            """
            
            contents = [types.Content(role="user", parts=[types.Part.from_text(text=prompt)])]
            config = types.GenerateContentConfig(response_mime_type="application/json")
            
            response = await asyncio.to_thread(
                self.gemini_client.models.generate_content,
                model=self.gemini_model,
                contents=contents,
                config=config
            )
            
            return json.loads(response.text)
            
        except Exception as e:
            return {"matches": False, "confidence": 0.0, "analysis": f"Error: {e}"}
    
    async def analyze_image_with_gemini(self, image_data: bytes, criteria: str) -> dict:
        """Analyze image with Gemini"""
        try:
            prompt = f"""
            Analyze this image for content matching: "{criteria}"
            
            Respond with JSON:
            {{
                "matches": true/false,
                "confidence": 0.0-1.0,
                "description": "what you see in the image",
                "analysis": "how it relates to the criteria"
            }}
            """
            
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_bytes(mime_type="image/jpeg", data=image_data),
                        types.Part.from_text(text=prompt)
                    ]
                )
            ]
            
            config = types.GenerateContentConfig(response_mime_type="application/json")
            
            response = await asyncio.to_thread(
                self.gemini_client.models.generate_content,
                model=self.gemini_model,
                contents=contents,
                config=config
            )
            
            return json.loads(response.text)
            
        except Exception as e:
            return {"matches": False, "confidence": 0.0, "analysis": f"Error: {e}"}
    
    async def download_image(self, image_url: str) -> bytes:
        """Download image from URL"""
        try:
            # Use browser to download image
            response = await self.tab.execute_script(f"""
                return fetch('{image_url}')
                    .then(response => response.arrayBuffer())
                    .then(buffer => Array.from(new Uint8Array(buffer)));
            """)
            
            if response and isinstance(response, list):
                return bytes(response)
            
        except Exception as e:
            print(f"Error downloading image: {e}")
        
        return b""


async def main():
    """Main function - Facebook Tour Scraper with Gemini AI"""
    # Get API key
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ Please set GEMINI_API_KEY environment variable")
        print("Get your API key from: https://aistudio.google.com/app/apikey")
        return

    print("🚀 Facebook Tour Scraper with Gemini AI")
    print("=" * 50)

    # Search parameters
    print("Enter the EXACT string you want to search for on Facebook:")
    print("Examples:")
    print("  - 'Smith' (to find people with last name Smith)")
    print("  - 'John Smith' (to find specific person)")
    print("  - 'Bhat' (to find people with last name Bhat)")

    search_string = input("\nExact search string: ").strip()
    if not search_string:
        print("❌ Search string is required!")
        return

    search_criteria = input("What are you looking for in their posts? (e.g., 'travel to Malaysia', 'vacation posts'): ").strip()
    if not search_criteria:
        search_criteria = "travel and vacation posts"

    max_users = int(input("Max users to analyze (default 5): ").strip() or "5")
    max_posts = int(input("Max posts per user (default 10): ").strip() or "10")

    print(f"\n🎯 Search Configuration:")
    print(f"   Search String: '{search_string}'")
    print(f"   Looking for: {search_criteria}")
    print(f"   Max Users: {max_users}")
    print(f"   Max Posts per User: {max_posts}")

    # Ask for analysis method
    print(f"\n📊 Analysis Options:")
    print("1. Code-based scraping (extracts text and images)")
    print("2. Screenshot analysis (Gemini analyzes page visually)")
    print("3. Both methods (most comprehensive)")

    analysis_method = input("Choose analysis method (1/2/3): ").strip() or "1"

    async with FacebookScraperV2(gemini_api_key) as scraper:
        # Login
        login_success = await scraper.manual_login()
        if not login_success:
            print("❌ Login failed!")
            return

        print("✅ Login successful!")

        # Search for users using exact search string
        print(f"\n🔍 Searching for people with: '{search_string}'")
        users = await scraper.search_users_by_exact_string(search_string, max_users)

        if not users:
            print("❌ No users found for that search")
            return

        print(f"✅ Found {len(users)} users for '{search_string}'")

        # Show found users
        for i, user in enumerate(users, 1):
            print(f"   {i}. {user['name']}")
            print(f"      URL: {user['profile_url']}")

        # Ask user if they want to continue
        if input(f"\nFound {len(users)} users. Press Enter to start analyzing their posts, or 'q' to quit: ").strip().lower() == 'q':
            return

        print(f"\n🎯 Analyzing {len(users)} profiles for: '{search_criteria}'")

        all_results = []

        for i, user in enumerate(users, 1):
            print(f"\n👤 Profile {i}/{len(users)}: {user['name']}")
            print(f"🔗 URL: {user['profile_url']}")

            # Ask user if they want to analyze this profile
            user_choice = input(f"Analyze {user['name']}'s posts? (Enter to continue, 'q' to quit, 's' to skip): ").strip().lower()
            if user_choice == 'q':
                break
            elif user_choice == 's':
                continue

            # Visit profile
            success = await scraper.visit_profile_directly(user['profile_url'])
            if not success:
                print("❌ Failed to load profile")
                continue

            # Ask for analysis method for this profile
            if analysis_method == "3":
                print("Choose analysis for this profile:")
                print("1. Code-based scraping")
                print("2. Screenshot analysis")
                print("3. Both methods")
                profile_analysis_choice = input("Choice (1/2/3): ").strip() or "1"
            else:
                profile_analysis_choice = analysis_method

            profile_results = {
                "user_name": user['name'],
                "profile_url": user['profile_url'],
                "search_criteria": search_criteria,
                "timestamp": int(time.time())
            }

            if profile_analysis_choice in ["2", "3"]:
                # Screenshot analysis
                print("📸 Taking screenshot...")
                screenshot = await scraper.take_screenshot()

                print("🤖 Analyzing screenshot with Gemini...")
                screenshot_analysis = await scraper.analyze_screenshot_with_gemini(screenshot, search_criteria)
                profile_results["screenshot_analysis"] = screenshot_analysis

                print(f"   Screenshot - Matches: {screenshot_analysis.get('matches', False)}, Confidence: {screenshot_analysis.get('confidence', 0.0):.2f}")

            if profile_analysis_choice in ["1", "3"]:
                # Text + Image scraping
                print("📜 Scraping posts and images...")
                scraped_content = await scraper.scrape_posts_text_and_images()

                print("🤖 Analyzing scraped content with Gemini...")
                content_analysis = await scraper.analyze_scraped_content(scraped_content, search_criteria)
                profile_results["content_analysis"] = content_analysis
                profile_results["scraped_content"] = scraped_content

                print(f"   Content - Matches: {content_analysis.get('overall_match', False)}, Confidence: {content_analysis.get('combined_confidence', 0.0):.2f}")

            all_results.append(profile_results)

            # Ask if user wants to continue
            if i < len(users):
                continue_choice = input("Continue to next profile? (y/n): ").strip().lower()
                if continue_choice == 'n':
                    break

        # Save all results
        timestamp = int(time.time())
        safe_criteria = "".join(c for c in search_criteria if c.isalnum() or c in (' ', '-', '_')).rstrip()[:30]
        safe_search_string = "".join(c for c in search_string if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
        output_file = f"facebook_analysis_{safe_search_string.replace(' ', '_')}_{safe_criteria.replace(' ', '_')}_{timestamp}.json"

        # Create results directory if it doesn't exist
        os.makedirs("results", exist_ok=True)
        output_path = os.path.join("results", output_file)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump({
                'search_string': search_string,
                'search_criteria': search_criteria,
                'analysis_method': analysis_method,
                'profiles_analyzed': len(all_results),
                'results': all_results,
                'timestamp': timestamp
            }, f, indent=2, ensure_ascii=False)

        print(f"\n💾 All results saved to: {output_path}")

        # Summary
        matching_profiles = []
        for result in all_results:
            screenshot_match = result.get("screenshot_analysis", {}).get("matches", False)
            content_match = result.get("content_analysis", {}).get("overall_match", False)

            if screenshot_match or content_match:
                matching_profiles.append(result["user_name"])

        print(f"\n📊 FINAL ANALYSIS SUMMARY")
        print("="*50)
        print(f"👥 Users found: {len(users)}")
        print(f"📝 Profiles analyzed: {len(all_results)}")
        print(f"✅ Matching profiles: {len(matching_profiles)}")
        print(f"🎯 Search criteria: '{search_criteria}'")

        if matching_profiles:
            print(f"\n✅ Profiles with matching content:")
            for name in matching_profiles:
                print(f"   • {name}")

        # Show some example findings
        if all_results:
            print(f"\n📋 Example findings:")
            for result in all_results[:3]:
                print(f"\n   👤 {result['user_name']}:")

                # Screenshot analysis
                screenshot_analysis = result.get("screenshot_analysis", {})
                if screenshot_analysis:
                    print(f"      📸 Screenshot: {screenshot_analysis.get('matches', False)} (confidence: {screenshot_analysis.get('confidence', 0.0):.2f})")
                    if screenshot_analysis.get('findings'):
                        print(f"         Findings: {', '.join(screenshot_analysis['findings'][:3])}")

                # Content analysis
                content_analysis = result.get("content_analysis", {})
                if content_analysis:
                    print(f"      📜 Content: {content_analysis.get('overall_match', False)} (confidence: {content_analysis.get('combined_confidence', 0.0):.2f})")
                    text_analysis = content_analysis.get('text_analysis', {})
                    if text_analysis and text_analysis.get('keywords'):
                        print(f"         Keywords: {', '.join(text_analysis['keywords'][:5])}")

        print(f"\n🎉 Analysis complete! Check the results folder for detailed output.")


if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass

    asyncio.run(main())
