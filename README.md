# Facebook Tour Scraper with Gemini AI

A powerful Facebook scraper that searches for users by location and last name, then analyzes their posts for travel-related content using Google's Gemini AI. Perfect for identifying people who have been on tours or traveled to specific destinations.

## 🌟 Features

- **Smart User Search**: Find Facebook users by location and last name
- **Intelligent Post Analysis**: Uses Gemini AI to analyze both text and images for travel content
- **Travel Detection**: Identifies mentions of destinations, travel activities, and tour experiences
- **Image Analysis**: Analyzes photos for tourist attractions, landmarks, and travel scenes
- **Comprehensive Results**: Exports detailed analysis in JSON and CSV formats
- **Cloudflare Bypass**: Built-in protection against anti-bot measures
- **Human-like Behavior**: Mimics real user interactions to avoid detection

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd FacebookTourScraper

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Copy the environment template and fill in your credentials:

```bash
cp .env.template .env
```

Edit `.env` with your actual values:

```env
FACEBOOK_EMAIL=<EMAIL>
FACEBOOK_PASSWORD=your_facebook_password
GEMINI_API_KEY=your_gemini_api_key_here
SEARCH_LOCATION=Malaysia
SEARCH_LAST_NAME=Smith
```

### 3. Get Gemini API Key

1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file

### 4. Run the Scraper

```bash
# Basic usage
python example_usage.py

# Or run the main script directly
python facebook_scraper.py
```

## 📖 Usage Examples

### Basic Search

```python
import asyncio
from facebook_scraper import FacebookScraper

async def search_travelers():
    async with FacebookScraper(gemini_api_key="your_key") as scraper:
        # Login
        await scraper.login_to_facebook("email", "password")
        
        # Search and analyze
        results = await scraper.search_and_analyze(
            location="Malaysia",
            last_name="Smith",
            max_users=10,
            max_posts_per_user=5
        )
        
        print(f"Found {results['summary']['travel_posts_found']} travel posts")

asyncio.run(search_travelers())
```

### Advanced Analysis

```python
# Analyze specific posts
posts = await scraper.get_user_posts(user, max_posts=20)
travel_results = await scraper.analyze_posts_for_travel(posts)

for post, analysis in travel_results:
    if analysis.confidence_score > 0.8:
        print(f"High confidence travel post: {analysis.destinations_mentioned}")
```

## 🔧 Configuration Options

| Parameter | Description | Default |
|-----------|-------------|---------|
| `SEARCH_LOCATION` | Target location to search | "Malaysia" |
| `SEARCH_LAST_NAME` | Last name to search for | "Smith" |
| `MAX_USERS` | Maximum users to analyze | 10 |
| `MAX_POSTS_PER_USER` | Posts per user to analyze | 10 |
| `HEADLESS` | Run browser in headless mode | false |
| `OUTPUT_FORMAT` | Output format (json/csv/both) | "json" |

## 📊 Output Format

### JSON Results
```json
{
  "search_params": {
    "location": "Malaysia",
    "last_name": "Smith"
  },
  "users_found": [...],
  "travel_posts": [
    {
      "user": {
        "name": "John Smith",
        "location": "Malaysia"
      },
      "content": "Just visited the beautiful temples in Bali...",
      "travel_analysis": {
        "is_travel_related": true,
        "confidence_score": 0.95,
        "destinations_mentioned": ["Bali"],
        "travel_type": "vacation"
      }
    }
  ],
  "summary": {
    "total_users": 5,
    "travel_posts_found": 12,
    "top_destinations": [...]
  }
}
```

### CSV Export
The scraper also exports results to CSV with columns:
- User Name
- Location  
- Post Content
- Travel Confidence Score
- Destinations Mentioned
- Travel Type

## 🤖 AI Analysis Features

The Gemini AI analyzer detects:

### Text Analysis
- Travel keywords (vacation, trip, tour, etc.)
- Destination mentions (countries, cities, landmarks)
- Travel activities (sightseeing, exploring, etc.)
- Transportation references (flight, hotel, etc.)

### Image Analysis
- Tourist attractions and landmarks
- Travel activities and scenes
- Transportation (planes, trains, etc.)
- Natural landscapes and vacation settings

## ⚠️ Important Notes

### Legal and Ethical Considerations
- Only scrape public information
- Respect Facebook's Terms of Service
- Use responsibly and ethically
- Consider privacy implications
- Don't use for spam or harassment

### Rate Limiting
- Built-in delays between requests
- Respects Facebook's rate limits
- Configurable timing parameters

### Security
- Never commit credentials to version control
- Use environment variables for sensitive data
- Consider using a dedicated Facebook account

## 🛠️ Troubleshooting

### Common Issues

**Login Failed**
- Check credentials in `.env` file
- Verify Facebook account is not locked
- Try disabling 2FA temporarily

**No Users Found**
- Try different search terms
- Check if location exists on Facebook
- Verify you're logged in successfully

**Gemini API Errors**
- Verify API key is correct
- Check API quota limits
- Ensure proper internet connection

**Browser Issues**
- Try running in non-headless mode
- Check Chrome/Chromium installation
- Update browser if needed

## 📝 License

This project is for educational and research purposes only. Please use responsibly and in accordance with Facebook's Terms of Service and applicable laws.

## 🤝 Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Search existing issues
3. Create a new issue with detailed information

---

**Disclaimer**: This tool is for educational and research purposes only. Users are responsible for complying with Facebook's Terms of Service and applicable laws.
