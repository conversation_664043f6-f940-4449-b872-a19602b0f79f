"""
Test script to verify the Facebook Tour Scraper setup
"""

import asyncio
import os
import sys
from pathlib import Path

def test_dependencies():
    """Test if all required dependencies are installed"""
    print("🔍 Testing dependencies...")
    
    required_packages = [
        'pydoll',
        'google.genai',
        'aiofiles'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - OK")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies installed!")
    return True


def test_environment():
    """Test environment variables"""
    print("\n🔍 Testing environment variables...")

    # Only Gemini API key is required
    required_vars = ['GEMINI_API_KEY']
    optional_vars = ['FACEBOOK_EMAIL', 'FACEBOOK_PASSWORD']

    missing_required = []

    # Check required variables
    for var in required_vars:
        value = os.getenv(var)
        if value and value.strip():
            print(f"✅ {var} - SET")
        else:
            print(f"❌ {var} - NOT SET (REQUIRED)")
            missing_required.append(var)

    # Check optional variables
    for var in optional_vars:
        value = os.getenv(var)
        if value and value.strip():
            print(f"✅ {var} - SET (for automatic login)")
        else:
            print(f"ℹ️  {var} - NOT SET (manual login will be used)")

    if missing_required:
        print(f"\n❌ Missing required environment variables: {', '.join(missing_required)}")
        print("Set GEMINI_API_KEY in your .env file")
        return False

    print("✅ Required environment variables set!")
    print("ℹ️  Facebook credentials are optional (manual login available)")
    return True


async def test_gemini_connection():
    """Test Gemini AI connection"""
    print("\n🔍 Testing Gemini AI connection...")
    
    try:
        from google import genai
        from google.genai import types
        
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            print("❌ GEMINI_API_KEY not set")
            return False
        
        client = genai.Client(api_key=api_key)
        
        # Test with a simple request
        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text="Hello, this is a test. Please respond with 'OK'.")]
            )
        ]
        
        response = await asyncio.to_thread(
            client.models.generate_content,
            model="gemini-2.5-flash-preview-04-17",
            contents=contents
        )
        
        if response and response.text:
            print("✅ Gemini AI connection - OK")
            print(f"   Response: {response.text[:50]}...")
            return True
        else:
            print("❌ Gemini AI connection - No response")
            return False
            
    except Exception as e:
        print(f"❌ Gemini AI connection failed: {e}")
        return False


async def test_browser():
    """Test browser functionality"""
    print("\n🔍 Testing browser functionality...")
    
    try:
        from pydoll.browser import Chrome
        from pydoll.browser.options import ChromiumOptions
        
        options = ChromiumOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        browser = Chrome(options=options)
        await browser.start()
        tab = await browser.get_page()
        
        # Test navigation
        await tab.go_to('https://httpbin.org/get')
        await asyncio.sleep(2)
        
        current_url = await tab.current_url
        if 'httpbin.org' in current_url:
            print("✅ Browser functionality - OK")
            await browser.stop()
            return True
        else:
            print("❌ Browser navigation failed")
            await browser.stop()
            return False
            
    except Exception as e:
        print(f"❌ Browser test failed: {e}")
        return False


def test_file_structure():
    """Test if all required files exist"""
    print("\n🔍 Testing file structure...")
    
    required_files = [
        'facebook_scraper.py',
        'config.py',
        'requirements.txt',
        'README.md'
    ]
    
    missing_files = []
    
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file} - EXISTS")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files present!")
    return True


async def main():
    """Run all tests"""
    print("🧪 Facebook Tour Scraper - Setup Test")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Dependencies", test_dependencies),
        ("Environment", test_environment),
        ("Gemini AI", test_gemini_connection),
        ("Browser", test_browser)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTests passed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your setup is ready.")
        print("You can now run: python example_usage.py")
    else:
        print("\n⚠️  Some tests failed. Please fix the issues above.")
        sys.exit(1)


if __name__ == "__main__":
    # Load environment variables from .env file if it exists
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("Note: python-dotenv not installed. Using system environment variables only.")
    
    asyncio.run(main())
