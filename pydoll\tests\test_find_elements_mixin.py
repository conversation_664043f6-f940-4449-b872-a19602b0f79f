import pytest
import re
from unittest.mock import Async<PERSON>ock, MagicMock, patch

from pydoll.elements.mixins.find_elements_mixin import FindElementsMixin
from pydoll.constants import By
from pydoll.exceptions import ElementNotFound, WaitElementTimeout


class MockFindElementsMixin(FindElementsMixin):
    """Mock implementation of FindElementsMixin for testing."""
    
    def __init__(self):
        self._connection_handler = AsyncMock()
        # Some tests need object_id, others don't
        self._object_id = None


class TestBuildXPath:
    """Test the _build_xpath static method comprehensively."""

    def test_build_xpath_single_id(self):
        """Test XPath building with only ID."""
        xpath = FindElementsMixin._build_xpath(id='test-id')
        assert xpath == '//*[@id="test-id"]'

    def test_build_xpath_single_class_name(self):
        """Test XPath building with only class name."""
        xpath = FindElementsMixin._build_xpath(class_name='btn-primary')
        expected = '//*[contains(concat(" ", normalize-space(@class), " "), " btn-primary ")]'
        assert xpath == expected

    def test_build_xpath_single_name(self):
        """Test XPath building with only name attribute."""
        xpath = FindElementsMixin._build_xpath(name='username')
        assert xpath == '//*[@name="username"]'

    def test_build_xpath_single_tag_name(self):
        """Test XPath building with only tag name."""
        xpath = FindElementsMixin._build_xpath(tag_name='button')
        assert xpath == '//button'

    def test_build_xpath_single_text(self):
        """Test XPath building with only text content."""
        xpath = FindElementsMixin._build_xpath(text='Click me')
        assert xpath == '//*[contains(text(), "Click me")]'

    def test_build_xpath_single_custom_attribute(self):
        """Test XPath building with single custom attribute."""
        xpath = FindElementsMixin._build_xpath(data_testid='submit-btn')
        assert xpath == '//*[@data_testid="submit-btn"]'

    def test_build_xpath_id_and_class(self):
        """Test XPath building with ID and class name."""
        xpath = FindElementsMixin._build_xpath(id='main-btn', class_name='primary')
        expected = '//*[@id="main-btn" and contains(concat(" ", normalize-space(@class), " "), " primary ")]'
        assert xpath == expected

    def test_build_xpath_tag_and_attributes(self):
        """Test XPath building with tag name and multiple attributes."""
        xpath = FindElementsMixin._build_xpath(
            tag_name='input',
            id='email-field',
            name='email',
            type='email'
        )
        expected = '//input[@id="email-field" and @name="email" and @type="email"]'
        assert xpath == expected

    def test_build_xpath_all_parameters(self):
        """Test XPath building with all possible parameters."""
        xpath = FindElementsMixin._build_xpath(
            id='complex-element',
            class_name='form-control',
            name='user_input',
            tag_name='input',
            text='placeholder text',
            data_role='textbox',
            aria_label='User input field'
        )
        expected = ('//input[@id="complex-element" and '
                   'contains(concat(" ", normalize-space(@class), " "), " form-control ") and '
                   '@name="user_input" and '
                   'contains(text(), "placeholder text") and '
                   '@data_role="textbox" and '
                   '@aria_label="User input field"]')
        assert xpath == expected

    def test_build_xpath_text_with_quotes(self):
        """Test XPath building with text containing quotes."""
        xpath = FindElementsMixin._build_xpath(text='Say "Hello"')
        assert xpath == '//*[contains(text(), "Say "Hello"")]'

    def test_build_xpath_attribute_with_quotes(self):
        """Test XPath building with attribute value containing quotes."""
        xpath = FindElementsMixin._build_xpath(title='This is a "quoted" title')
        assert xpath == '//*[@title="This is a "quoted" title"]'

    def test_build_xpath_empty_values_ignored(self):
        """Test that empty string values are ignored in XPath building."""
        xpath = FindElementsMixin._build_xpath(
            id='test-id',
            class_name='',  # Empty string should be ignored
            name=None,      # None should be ignored
            tag_name='div'
        )
        assert xpath == '//div[@id="test-id"]'

    def test_build_xpath_class_name_with_spaces(self):
        """Test XPath building with class name that has spaces (edge case)."""
        xpath = FindElementsMixin._build_xpath(class_name='btn primary large')
        expected = '//*[contains(concat(" ", normalize-space(@class), " "), " btn primary large ")]'
        assert xpath == expected

    def test_build_xpath_special_characters_in_attributes(self):
        """Test XPath building with special characters in attribute values."""
        xpath = FindElementsMixin._build_xpath(
            data_value='<EMAIL>',
            aria_describedby='field-help-123'
        )
        expected = '//*[@data_value="<EMAIL>" and @aria_describedby="field-help-123"]'
        assert xpath == expected

    def test_build_xpath_numeric_attribute_values(self):
        """Test XPath building with numeric attribute values."""
        xpath = FindElementsMixin._build_xpath(
            tabindex='0',
            maxlength='255'
        )
        expected = '//*[@tabindex="0" and @maxlength="255"]'
        assert xpath == expected

    def test_build_xpath_no_parameters(self):
        """Test XPath building with no parameters returns generic selector."""
        xpath = FindElementsMixin._build_xpath()
        assert xpath == '//*'

    def test_build_xpath_only_tag_name(self):
        """Test XPath building with only tag name."""
        xpath = FindElementsMixin._build_xpath(tag_name='span')
        assert xpath == '//span'

    def test_build_xpath_hyphenated_attributes(self):
        """Test XPath building with hyphenated attribute names."""
        xpath = FindElementsMixin._build_xpath(
            **{'data-test-id': 'submit-button', 'aria-label': 'Submit form'}
        )
        expected = '//*[@data-test-id="submit-button" and @aria-label="Submit form"]'
        assert xpath == expected


class TestGetExpressionType:
    """Test the _get_expression_type static method."""

    def test_xpath_double_slash(self):
        """Test XPath detection with double slash."""
        assert FindElementsMixin._get_expression_type('//div') == By.XPATH

    def test_xpath_dot_double_slash(self):
        """Test XPath detection with dot double slash."""
        assert FindElementsMixin._get_expression_type('.//span') == By.XPATH

    def test_xpath_dot_slash(self):
        """Test XPath detection with dot slash."""
        assert FindElementsMixin._get_expression_type('./button') == By.XPATH

    def test_xpath_single_slash(self):
        """Test XPath detection with single slash."""
        assert FindElementsMixin._get_expression_type('/html/body') == By.XPATH

    def test_id_selector(self):
        """Test ID selector detection."""
        assert FindElementsMixin._get_expression_type('#main-content') == By.ID

    def test_class_selector(self):
        """Test class selector detection."""
        assert FindElementsMixin._get_expression_type('.btn-primary') == By.CLASS_NAME

    def test_class_selector_not_xpath(self):
        """Test that class selector doesn't conflict with XPath dot slash."""
        assert FindElementsMixin._get_expression_type('.button') == By.CLASS_NAME
        assert FindElementsMixin._get_expression_type('./button') == By.XPATH

    def test_css_selector_default(self):
        """Test CSS selector as default."""
        assert FindElementsMixin._get_expression_type('div.content > p') == By.CSS_SELECTOR

    def test_css_selector_attribute(self):
        """Test CSS selector with attributes."""
        assert FindElementsMixin._get_expression_type('input[type="text"]') == By.CSS_SELECTOR

    def test_css_selector_pseudo_class(self):
        """Test CSS selector with pseudo-classes."""
        assert FindElementsMixin._get_expression_type('button:hover') == By.CSS_SELECTOR

    def test_complex_xpath_expressions(self):
        """Test complex XPath expressions are detected correctly."""
        complex_xpaths = [
            '//div[@class="content"]/p[contains(text(), "Hello")]',
            './/button[position()=1]',
            './/*[@id="test" and @class="active"]',
            '/html/body/div[1]/form/input[@type="submit"]'
        ]
        for xpath in complex_xpaths:
            assert FindElementsMixin._get_expression_type(xpath) == By.XPATH

    def test_edge_case_expressions(self):
        """Test edge case expressions."""
        # Empty string should default to CSS
        assert FindElementsMixin._get_expression_type('') == By.CSS_SELECTOR
        
        # Just a dot should be class selector
        assert FindElementsMixin._get_expression_type('.') == By.CLASS_NAME
        
        # Just a hash should be ID selector
        assert FindElementsMixin._get_expression_type('#') == By.ID


class TestEnsureRelativeXPath:
    """Test the _ensure_relative_xpath static method."""

    def test_absolute_xpath_becomes_relative(self):
        """Test that absolute XPath becomes relative."""
        xpath = '//div[@id="test"]'
        result = FindElementsMixin._ensure_relative_xpath(xpath)
        assert result == './/div[@id="test"]'

    def test_already_relative_xpath_unchanged(self):
        """Test that already relative XPath remains unchanged."""
        xpath = './/div[@id="test"]'
        result = FindElementsMixin._ensure_relative_xpath(xpath)
        assert result == './/div[@id="test"]'

    def test_dot_slash_xpath_unchanged(self):
        """Test that dot slash XPath remains unchanged."""
        xpath = './button'
        result = FindElementsMixin._ensure_relative_xpath(xpath)
        assert result == './button'

    def test_single_slash_xpath_becomes_relative(self):
        """Test that single slash XPath becomes relative."""
        xpath = '/html/body/div'
        result = FindElementsMixin._ensure_relative_xpath(xpath)
        assert result == './html/body/div'

    def test_empty_xpath(self):
        """Test empty XPath handling."""
        xpath = ''
        result = FindElementsMixin._ensure_relative_xpath(xpath)
        assert result == '.'

    def test_complex_xpath_expressions(self):
        """Test complex XPath expressions."""
        test_cases = [
            ('//div[contains(@class, "test")]', './/div[contains(@class, "test")]'),
            ('.//span[@id="existing"]', './/span[@id="existing"]'),
            ('//*[@data-test="value"]', './/*[@data-test="value"]'),
            ('//button[text()="Submit"]', './/button[text()="Submit"]')
        ]
        
        for input_xpath, expected in test_cases:
            result = FindElementsMixin._ensure_relative_xpath(input_xpath)
            assert result == expected


class TestGetByAndValue:
    """Test the _get_by_and_value method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mixin = MockFindElementsMixin()
        self.by_map = {
            'id': By.ID,
            'class_name': By.CLASS_NAME,
            'name': By.NAME,
            'tag_name': By.TAG_NAME,
            'xpath': By.XPATH,
        }

    def test_single_id_selector(self):
        """Test single ID selector returns direct By.ID."""
        by, value = self.mixin._get_by_and_value(self.by_map, id='test-id')
        assert by == By.ID
        assert value == 'test-id'

    def test_single_class_name_selector(self):
        """Test single class name selector returns direct By.CLASS_NAME."""
        by, value = self.mixin._get_by_and_value(self.by_map, class_name='btn-primary')
        assert by == By.CLASS_NAME
        assert value == 'btn-primary'

    def test_single_name_selector(self):
        """Test single name selector returns direct By.NAME."""
        by, value = self.mixin._get_by_and_value(self.by_map, name='username')
        assert by == By.NAME
        assert value == 'username'

    def test_single_tag_name_selector(self):
        """Test single tag name selector returns direct By.TAG_NAME."""
        by, value = self.mixin._get_by_and_value(self.by_map, tag_name='button')
        assert by == By.TAG_NAME
        assert value == 'button'

    def test_single_custom_attribute(self):
        """Test single custom attribute builds XPath."""
        by, value = self.mixin._get_by_and_value(self.by_map, data_testid='submit-btn')
        assert by == By.XPATH
        assert value == '//*[@data_testid="submit-btn"]'

    def test_multiple_attributes_build_xpath(self):
        """Test multiple attributes build XPath."""
        by, value = self.mixin._get_by_and_value(
            self.by_map, 
            id='test-id', 
            class_name='btn-primary'
        )
        assert by == By.XPATH
        expected = '//*[@id="test-id" and contains(concat(" ", normalize-space(@class), " "), " btn-primary ")]'
        assert value == expected

    def test_text_with_single_attribute_builds_xpath(self):
        """Test that text with any other attribute builds XPath."""
        by, value = self.mixin._get_by_and_value(
            self.by_map,
            id='test-id',
            text='Click me'
        )
        assert by == By.XPATH
        expected = '//*[@id="test-id" and contains(text(), "Click me")]'
        assert value == expected

    def test_text_alone_builds_xpath(self):
        """Test that text alone builds XPath."""
        by, value = self.mixin._get_by_and_value(self.by_map, text='Submit')
        assert by == By.XPATH
        assert value == '//*[contains(text(), "Submit")]'

    def test_empty_values_ignored(self):
        """Test that empty values are ignored in selector building."""
        by, value = self.mixin._get_by_and_value(
            self.by_map,
            id='test-id',
            class_name='',  # Empty string
            name=None       # None value
        )
        assert by == By.ID
        assert value == 'test-id'

    def test_all_empty_values_with_custom_attribute(self):
        """Test custom attribute when standard attributes are empty."""
        by, value = self.mixin._get_by_and_value(
            self.by_map,
            id='',
            class_name=None,
            data_role='button'
        )
        assert by == By.XPATH
        assert value == '//*[@data_role="button"]'


class TestFindElementsMixinEdgeCases:
    """Test edge cases and error conditions in FindElementsMixin."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mixin = MockFindElementsMixin()

    @pytest.mark.asyncio
    async def test_find_no_criteria_raises_error(self):
        """Test that find with no criteria raises ValueError."""
        with pytest.raises(ValueError, match='At least one of the following arguments must be provided'):
            await self.mixin.find()

    @pytest.mark.asyncio
    async def test_find_empty_string_criteria_raises_error(self):
        """Test that find with only empty string criteria raises ValueError."""
        with pytest.raises(ValueError, match='At least one of the following arguments must be provided'):
            await self.mixin.find(id='', class_name='', name='', tag_name='', text='')

    @pytest.mark.asyncio
    async def test_find_none_criteria_raises_error(self):
        """Test that find with only None criteria raises ValueError."""
        with pytest.raises(ValueError, match='At least one of the following arguments must be provided'):
            await self.mixin.find(id=None, class_name=None, name=None, tag_name=None, text=None)

    @pytest.mark.asyncio
    async def test_find_with_custom_attributes_only(self):
        """Test find with only custom attributes works."""
        # Mock the internal methods
        self.mixin._find_element = AsyncMock(return_value=MagicMock())
        
        result = await self.mixin.find(data_testid='submit-button')
        
        # Should call _find_element with XPath
        self.mixin._find_element.assert_called_once()
        call_args = self.mixin._find_element.call_args[0]
        assert call_args[0] == By.XPATH
        assert '@data_testid="submit-button"' in call_args[1]

    @pytest.mark.asyncio
    async def test_query_empty_expression(self):
        """Test query with empty expression."""
        self.mixin._find_element = AsyncMock(return_value=MagicMock())
        
        result = await self.mixin.query('')
        
        # Should call _find_element with CSS_SELECTOR (default)
        self.mixin._find_element.assert_called_once()
        call_args = self.mixin._find_element.call_args[0]
        assert call_args[0] == By.CSS_SELECTOR
        assert call_args[1] == ''

    @pytest.mark.asyncio
    async def test_find_or_wait_element_timeout_zero(self):
        """Test find_or_wait_element with timeout=0 calls find immediately."""
        self.mixin._find_element = AsyncMock(return_value=MagicMock())
        
        result = await self.mixin.find_or_wait_element(By.ID, 'test-id', timeout=0)
        
        self.mixin._find_element.assert_called_once_with(By.ID, 'test-id', raise_exc=True)

    @pytest.mark.asyncio
    async def test_find_or_wait_element_timeout_success_on_retry(self):
        """Test find_or_wait_element succeeds on retry within timeout."""
        # First call returns None, second call returns element
        mock_element = MagicMock()
        self.mixin._find_element = AsyncMock(side_effect=[None, mock_element])
        
        with patch('asyncio.sleep') as mock_sleep, \
             patch('asyncio.get_event_loop') as mock_loop:
            # Mock time progression
            mock_loop.return_value.time.side_effect = [0, 0.5, 1.0]
            
            result = await self.mixin.find_or_wait_element(
                By.ID, 'test-id', timeout=2, raise_exc=False
            )
        
        assert result == mock_element
        assert self.mixin._find_element.call_count == 2
        mock_sleep.assert_called_once_with(0.5)

    @pytest.mark.asyncio
    async def test_find_or_wait_element_timeout_failure(self):
        """Test find_or_wait_element raises WaitElementTimeout."""
        self.mixin._find_element = AsyncMock(return_value=None)
        
        with patch('asyncio.sleep') as mock_sleep, \
             patch('asyncio.get_event_loop') as mock_loop:
            # Mock time progression that exceeds timeout
            mock_loop.return_value.time.side_effect = [0, 0.5, 1.0, 1.5, 2.1]
            
            with pytest.raises(WaitElementTimeout):
                await self.mixin.find_or_wait_element(
                    By.ID, 'test-id', timeout=2, raise_exc=True
                )

    @pytest.mark.asyncio
    async def test_find_or_wait_element_timeout_failure_no_exception(self):
        """Test find_or_wait_element returns None when raise_exc=False."""
        self.mixin._find_element = AsyncMock(return_value=None)
        
        with patch('asyncio.sleep') as mock_sleep, \
             patch('asyncio.get_event_loop') as mock_loop:
            # Mock time progression that exceeds timeout
            mock_loop.return_value.time.side_effect = [0, 0.5, 1.0, 1.5, 2.1]
            
            result = await self.mixin.find_or_wait_element(
                By.ID, 'test-id', timeout=2, raise_exc=False
            )
        
        assert result is None

    @pytest.mark.asyncio
    async def test_find_elements_with_timeout(self):
        """Test find with find_all=True and timeout."""
        mock_elements = [MagicMock(), MagicMock()]
        self.mixin._find_elements = AsyncMock(return_value=mock_elements)
        
        result = await self.mixin.find_or_wait_element(
            By.CLASS_NAME, 'item', timeout=1, find_all=True
        )
        
        assert result == mock_elements
        self.mixin._find_elements.assert_called_once()

    def test_regex_pattern_in_get_expression_type(self):
        """Test the regex pattern used in _get_expression_type."""
        xpath_pattern = r'^(//|\.//|\.\/|/)'
        
        # Test cases that should match
        xpath_expressions = [
            '//div',
            './/span', 
            './button',
            '/html/body'
        ]
        
        for expr in xpath_expressions:
            assert re.match(xpath_pattern, expr), f"Pattern should match: {expr}"
        
        # Test cases that should not match
        non_xpath_expressions = [
            'div.class',
            '#id',
            '.class',
            'input[type="text"]',
            'button:hover'
        ]
        
        for expr in non_xpath_expressions:
            assert not re.match(xpath_pattern, expr), f"Pattern should not match: {expr}"

    def test_xpath_building_with_boolean_attributes(self):
        """Test XPath building with boolean-like attributes."""
        xpath = FindElementsMixin._build_xpath(
            required='true',
            disabled='false',
            checked='checked'
        )
        expected = '//*[@required="true" and @disabled="false" and @checked="checked"]'
        assert xpath == expected

    def test_xpath_building_preserves_attribute_order(self):
        """Test that XPath building maintains consistent attribute order."""
        # Test multiple times to ensure consistency
        for _ in range(5):
            xpath = FindElementsMixin._build_xpath(
                id='test',
                class_name='btn',
                name='submit',
                data_role='button'
            )
            # The order should be: id, class_name, name, then custom attributes
            assert '@id="test"' in xpath
            assert 'contains(concat(" ", normalize-space(@class), " "), " btn ")' in xpath
            assert '@name="submit"' in xpath
            assert '@data_role="button"' in xpath

    def test_xpath_building_with_unicode_characters(self):
        """Test XPath building with Unicode characters."""
        xpath = FindElementsMixin._build_xpath(
            text='Olá mundo',
            title='Título com acentos',
            placeholder='Escreva aqui...'
        )
        expected = '//*[contains(text(), "Olá mundo") and @title="Título com acentos" and @placeholder="Escreva aqui..."]'
        assert xpath == expected

    def test_class_name_xpath_normalization(self):
        """Test that class name XPath uses proper normalization."""
        xpath = FindElementsMixin._build_xpath(class_name='test-class')
        
        # Should use normalize-space to handle multiple spaces
        assert 'normalize-space(@class)' in xpath
        # Should wrap with spaces to match exact class names
        assert '" test-class "' in xpath
        # Should use concat to add spaces
        assert 'concat(" "' in xpath