site_name: Pyd<PERSON> - Async Web Automation Library
repo_url: https://github.com/autoscrape-labs/pydoll
repo_name: autoscrape-labs/pydoll

# Configuração da navegação
nav:
  - Pydoll: index.md
  - Features: features.md
  - Deep Dive:
      - Overview: deep-dive/index.md
      - Chrome DevTools Protocol: deep-dive/cdp.md
      - The Connection Layer: deep-dive/connection-layer.md      
      - The Browser Domain: deep-dive/browser-domain.md
      - The Tab Domain: deep-dive/tab-domain.md
      - The Find Elements Mixin: deep-dive/find-elements-mixin.md
      - The WebElement Domain: deep-dive/webelement-domain.md
      - The Event System: deep-dive/event-system.md
      - Network Capabilities: deep-dive/network-capabilities.md
  - API Reference:
      - Overview: api/index.md
      - Browser:
          - Chrome: api/browser/chrome.md
          - Edge: api/browser/edge.md
          - Options: api/browser/options.md
          - Tab: api/browser/tab.md
          - Managers: api/browser/managers.md
      - Elements:
          - WebElement: api/elements/web_element.md
          - Mixins: api/elements/mixins.md
      - Connection:
          - Connection Handler: api/connection/connection.md
          - Managers: api/connection/managers.md
      - Commands:
          - Overview: api/commands/index.md
          - Browser: api/commands/browser.md
          - DOM: api/commands/dom.md
          - Input: api/commands/input.md
          - Network: api/commands/network.md
          - Page: api/commands/page.md
          - Runtime: api/commands/runtime.md
          - Storage: api/commands/storage.md
          - Target: api/commands/target.md
          - Fetch: api/commands/fetch.md
      - Protocol:
          - Commands: api/protocol/commands.md
          - Events: api/protocol/events.md
      - Core:
          - Constants: api/core/constants.md
          - Exceptions: api/core/exceptions.md
          - Utils: api/core/utils.md

theme:
  name: material
  font:
    text: Roboto
    code: Roboto Mono
  palette:
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: custom
      accent: indigo
      toggle:
        icon: material/toggle-switch-off-outline
        name: Dark Mode
  
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: custom
      accent: indigo
      toggle:
        icon: material/toggle-switch
        name: Light Mode
  icon:
    repo: fontawesome/brands/github
    logo: material/lightning-bolt
  favicon: images/favicon.png
  features:
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.sections
    - navigation.indexes
    - navigation.expand
    - navigation.path
    - navigation.top
    - toc.follow
  
plugins:
  - search
  - mkdocstrings:
      handlers:
        python:
          options:
            show_root_heading: true
            show_if_no_docstring: true
            inherited_members: true
            members_order: source
            separate_signature: true
            filters:
            - '!^_'
            - '!^__'
            merge_init_into_class: true
            docstring_section_style: spacy
            signature_crossrefs: true
            show_symbol_type_heading: true
            show_symbol_type_toc: true
            show_source: false
            show_bases: true
            heading_level: 1

extra_css:
  - stylesheets/termynal.css
  - stylesheets/extra.css

extra_javascript:
  - scripts/termynal.js
  - scripts/extra.js
  - https://unpkg.com/mermaid@10.0.0/dist/mermaid.min.js

markdown_extensions:
  - pymdownx.critic
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.details
  - pymdownx.keys
  - footnotes
  - admonition
  - markdown.extensions.attr_list
  - pymdownx.tabbed:
      alternate_style: true
  - attr_list
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - md_in_html