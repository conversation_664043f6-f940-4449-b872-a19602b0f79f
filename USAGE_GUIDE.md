# Facebook Tour Scraper - Usage Guide

## 🎯 **How It Actually Works Now**

The scraper has been completely redesigned to work the way you requested:

### **1. Search by Last Name Only**
- Searches Facebook for people with a specific last name (e.g., "<PERSON><PERSON>")
- No location filtering in the search - finds all public profiles with that name
- Extracts profile URLs for individual analysis

### **2. Individual Profile Analysis**
- Visits each profile individually
- Scrapes posts from their timeline
- Analyzes each post with Gemini AI for your specific criteria

### **3. Manual Control Features**
- **Start/Stop Control**: You can stop the scraper at any point
- **User Confirmation**: Asks before analyzing each user's profile
- **Manual Login**: Intelligent login detection without hardcoded credentials

## 🚀 **Quick Start**

### **1. Set Up Environment**
```bash
# Only Gemini API key is required
echo "GEMINI_API_KEY=your_gemini_key_here" > .env
```

### **2. Run the Scraper**
```bash
python facebook_scraper.py
```

### **3. Follow the Interactive Prompts**
```
Enter last name to search for (e.g., <PERSON>hat): Bhat
What are you looking for? (e.g., 'travel to Malaysia'): travel to Malaysia
Max users to analyze (default 5): 5
Max posts per user (default 10): 10

🔐 Login Options:
1. Manual login (recommended - safer)
2. Automatic login (requires credentials in .env)
Choose login method (1 or 2): 1
```

## 🔐 **Intelligent Manual Login**

The login system now intelligently detects your login status:

### **Features:**
- ✅ **No Sleep Timers** - Responds immediately to login completion
- ✅ **2FA Support** - Handles two-factor authentication
- ✅ **Captcha Detection** - Recognizes and waits for captcha completion
- ✅ **Security Checkpoints** - Handles Facebook security checks
- ✅ **Smart Detection** - Multiple methods to confirm login success

### **Login Process:**
1. Browser opens to Facebook login page
2. You login manually (handle 2FA, captcha, etc.)
3. Scraper automatically detects successful login
4. Continues with search immediately

## 🎯 **Search and Analysis Workflow**

### **Step 1: Search by Last Name**
```
🔍 Searching for people with last name: Bhat
✅ Found 5 users with last name 'Bhat'

Found 5 users. Press Enter to start analyzing their posts, or 'q' to quit:
```

### **Step 2: Individual Profile Analysis**
```
👤 User 1/5: John Bhat
🔗 Profile: https://facebook.com/john.bhat

Analyze John Bhat's posts? (Enter to continue, 'q' to quit, 's' to skip):

📱 Now viewing profile: John Bhat
Press Enter to start scraping posts, or 'q' to skip this user:

📜 Scrolling to load posts for John Bhat
🔍 Searching for posts on John Bhat's profile
📝 Found 8 posts. Analyzing with Gemini AI...

   🤖 Analyzing post 1/8...
   ✅ Match found! Confidence: 0.95
   
   🤖 Analyzing post 2/8...
   ❌ No match (confidence: 0.2)
```

### **Step 3: Gemini AI Analysis**
For each post, Gemini AI analyzes:
- **Text content** for your specific criteria
- **Images** for visual matches
- **Confidence scoring** (0.0 to 1.0)
- **Destination extraction**
- **Travel type classification**

## 🛠️ **Manual Control Features**

### **During Execution:**
- **Enter** = Continue to next step
- **'q'** = Quit completely
- **'s'** = Skip current user
- **Ctrl+C** = Emergency stop

### **Programmatic Control:**
```python
# In another script or console
scraper.stop_scraper()    # Stop the scraper
scraper.pause_scraper()   # Pause execution
scraper.resume_scraper()  # Resume execution
```

## 📊 **Results and Output**

### **Real-time Feedback:**
```
📝 Post 1: Just got back from an amazing trip to Malaysia! The food was incredible...
📝 Post 2: Visited Kuala Lumpur last month, such a beautiful city...

🎉 Analysis complete!
📊 Total posts analyzed: 45
✅ Matching posts found: 8
```

### **Saved Files:**
- **JSON**: `results/facebook_analysis_Bhat_travel_to_Malaysia_1234567890.json`
- **CSV**: `results/facebook_analysis_Bhat_travel_to_Malaysia_1234567890.csv`

### **Summary Report:**
```
📊 FINAL ANALYSIS SUMMARY
👥 Users found: 5
📝 Posts analyzed: 45
✅ Matching posts found: 8

🌍 Top destinations mentioned:
   • Malaysia: 5 mentions
   • Kuala Lumpur: 3 mentions
   • Penang: 2 mentions

🎯 Analysis types found:
   • vacation: 5 posts
   • business: 2 posts
   • tour: 1 posts
```

## 🔧 **Advanced Usage**

### **Custom Search Criteria:**
```python
# Example criteria
"travel to Malaysia"
"vacation in Southeast Asia"
"business trip to KL"
"tour of Malaysian cities"
"visited Penang"
```

### **Batch Processing:**
```python
# Run multiple searches
criteria_list = [
    "travel to Malaysia",
    "vacation in Thailand", 
    "business trip to Singapore"
]

for criteria in criteria_list:
    results = await scraper.search_and_analyze_by_criteria(
        last_name="Bhat",
        search_criteria=criteria,
        max_users=10,
        max_posts_per_user=15
    )
```

## 🚨 **Important Notes**

### **Ethical Usage:**
- Only analyzes public posts
- Respects Facebook's rate limits
- Includes delays between requests
- Manual control prevents over-scraping

### **Privacy & Security:**
- No credentials stored in files
- Manual login reduces account risk
- Intelligent detection prevents bot flags
- User has full control over process

### **Performance:**
- Analyzes ~1-2 posts per minute (due to AI analysis)
- Handles 5-10 users in 30-60 minutes
- Saves progress continuously
- Can resume if interrupted

## 🧪 **Testing**

```bash
# Test the setup
python test_setup.py

# Test manual login
python test_manual_login.py

# Run example usage
python example_usage.py
```

This scraper now works exactly as you requested - it searches by last name, visits individual profiles, analyzes posts with AI, and gives you full manual control over the process!
