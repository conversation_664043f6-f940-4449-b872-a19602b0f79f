name: Refactoring Request
description: Suggest code refactoring to improve pydoll's quality, performance, or maintainability
title: "[Refactor]: "
labels: ["refactor", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        # pydoll Refactoring Request
        
        Thank you for suggesting improvements to our codebase. This form will guide you through providing the information needed to consider your refactoring suggestion effectively.
  
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist before suggesting refactoring
      description: Please make sure you've completed the following steps before submitting a refactoring request.
      options:
        - label: I have searched for [similar refactoring requests](https://github.com/thalissonvs/pydoll/issues) and didn't find a duplicate.
          required: true
        - label: I have reviewed the current implementation to ensure my understanding is accurate.
          required: true
  
  - type: textarea
    id: current_implementation
    attributes:
      label: Current Implementation
      description: Describe the current implementation and its limitations. Include file paths if known.
      placeholder: |
        The current implementation in `pydoll/module/file.py` has the following issues:
        1. It uses an inefficient algorithm for...
        2. The code structure makes it difficult to maintain because...
    validations:
      required: true
  
  - type: textarea
    id: proposed_changes
    attributes:
      label: Proposed Changes
      description: Describe the changes you're suggesting. Be as specific as possible.
      placeholder: |
        I suggest refactoring this code to:
        1. Replace the current algorithm with X, which would improve performance by...
        2. Restructure the class hierarchy to better separate concerns by...
        
        Example code sketch (if applicable):
        ```python
        def improved_method():
            # better implementation
        ```
    validations:
      required: true
  
  - type: textarea
    id: benefits
    attributes:
      label: Benefits
      description: Explain the benefits of this refactoring.
      placeholder: |
        This refactoring would:
        - Improve performance by X%
        - Make the code more maintainable by...
        - Reduce code complexity by...
        - Fix potential bugs such as...
    validations:
      required: true
  
  - type: dropdown
    id: impact
    attributes:
      label: API Impact
      description: Would this refactoring change the public API?
      options:
        - No API changes (internal refactoring only)
        - Minor API changes (backward compatible)
        - Breaking API changes
    validations:
      required: true
  
  - type: textarea
    id: testing_approach
    attributes:
      label: Testing Approach
      description: How can we verify that the refactoring doesn't break existing functionality?
      placeholder: |
        The refactoring can be tested by:
        - Running the existing test suite
        - Adding new tests for edge cases such as...
        - Benchmarking performance before and after
  
  - type: dropdown
    id: contribution
    attributes:
      label: Contribution
      description: Would you be willing to contribute this refactoring yourself?
      options:
        - Yes, I'd be willing to implement this refactoring
        - I could help with parts of the implementation
        - No, I don't have the capacity to implement this 