"""
Facebook Tour Scraper using pyDoll and Gemini LLM

This scraper searches for Facebook users by location and last name,
then analyzes their posts for travel/tour-related content using Gemini AI.
"""

import asyncio
import base64
import json
import logging
import os
import re
import time
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin, urlparse

from google import genai
from google.genai import types
from pydoll.browser import Chrome
from pydoll.browser.options import ChromiumOptions
from pydoll.constants import By
from pydoll.exceptions import WaitElementTimeout

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class FacebookUser:
    """Data class for Facebook user information"""
    name: str
    profile_url: str
    location: str
    profile_picture_url: Optional[str] = None
    mutual_friends: int = 0
    bio: Optional[str] = None


@dataclass
class FacebookPost:
    """Data class for Facebook post information"""
    user: FacebookUser
    post_id: str
    content: str
    timestamp: str
    images: List[str]
    likes: int = 0
    comments: int = 0
    shares: int = 0
    post_url: Optional[str] = None


@dataclass
class TravelAnalysis:
    """Data class for travel analysis results"""
    is_travel_related: bool
    confidence_score: float
    travel_keywords: List[str]
    destinations_mentioned: List[str]
    travel_type: Optional[str] = None  # vacation, business, tour, etc.
    analysis_summary: str = ""


class GeminiAnalyzer:
    """Handles travel content analysis using Gemini LLM"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.environ.get("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        self.client = genai.Client(api_key=self.api_key)
        self.model = "gemini-2.5-flash-preview-04-17"
    
    async def analyze_text_for_travel(self, text: str) -> TravelAnalysis:
        """Analyze text content for travel-related information"""
        
        prompt = f"""
        Analyze the following social media post text for travel-related content.
        
        Text to analyze: "{text}"
        
        Please provide a JSON response with the following structure:
        {{
            "is_travel_related": boolean,
            "confidence_score": float (0.0 to 1.0),
            "travel_keywords": [list of travel-related words found],
            "destinations_mentioned": [list of places/destinations mentioned],
            "travel_type": "vacation|business|tour|adventure|other|null",
            "analysis_summary": "brief explanation of the analysis"
        }}
        
        Look for indicators like:
        - Mentions of countries, cities, landmarks
        - Travel activities (sightseeing, touring, visiting)
        - Transportation (flight, hotel, airport)
        - Travel emotions (vacation, holiday, trip)
        - Location tags or check-ins
        """
        
        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=prompt)]
            )
        ]
        
        config = types.GenerateContentConfig(
            response_mime_type="application/json",
        )
        
        try:
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model,
                contents=contents,
                config=config
            )
            
            result = json.loads(response.text)
            return TravelAnalysis(**result)
            
        except Exception as e:
            logger.error(f"Error analyzing text with Gemini: {e}")
            return TravelAnalysis(
                is_travel_related=False,
                confidence_score=0.0,
                travel_keywords=[],
                destinations_mentioned=[],
                analysis_summary=f"Analysis failed: {str(e)}"
            )
    
    async def analyze_image_for_travel(self, image_data: bytes) -> TravelAnalysis:
        """Analyze image content for travel-related information"""
        
        image_b64 = base64.b64encode(image_data).decode('utf-8')
        
        prompt = """
        Analyze this image for travel-related content. Look for:
        - Tourist attractions, landmarks, monuments
        - Travel activities (sightseeing, tours, adventures)
        - Transportation (planes, trains, buses, boats)
        - Hotels, restaurants, vacation settings
        - Natural landscapes, beaches, mountains
        - Cultural sites, museums, historical places
        
        Provide a JSON response with the same structure as text analysis.
        """
        
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        mime_type="image/jpeg",
                        data=base64.b64decode(image_b64)
                    ),
                    types.Part.from_text(text=prompt)
                ]
            )
        ]
        
        config = types.GenerateContentConfig(
            response_mime_type="application/json",
        )
        
        try:
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model,
                contents=contents,
                config=config
            )
            
            result = json.loads(response.text)
            return TravelAnalysis(**result)
            
        except Exception as e:
            logger.error(f"Error analyzing image with Gemini: {e}")
            return TravelAnalysis(
                is_travel_related=False,
                confidence_score=0.0,
                travel_keywords=[],
                destinations_mentioned=[],
                analysis_summary=f"Image analysis failed: {str(e)}"
            )


class FacebookScraper:
    """Main Facebook scraper class using pyDoll"""
    
    def __init__(self, gemini_api_key: Optional[str] = None, headless: bool = False):
        self.gemini_analyzer = GeminiAnalyzer(gemini_api_key)
        self.headless = headless
        self.browser = None
        self.tab = None
        self.logged_in = False
        
    async def __aenter__(self):
        await self.start_browser()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close_browser()
    
    async def start_browser(self):
        """Initialize and start the browser"""
        options = ChromiumOptions()
        
        if self.headless:
            options.add_argument('--headless')
        
        # Add user agent to appear more human-like
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Additional options for better compatibility
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        self.browser = Chrome(options=options)
        await self.browser.start()
        self.tab = await self.browser.get_page()
        
        # Enable necessary events
        await self.tab.enable_page_events()
        await self.tab.enable_network_events()
        
        logger.info("Browser started successfully")
    
    async def close_browser(self):
        """Close the browser"""
        if self.browser:
            await self.browser.stop()
            logger.info("Browser closed")

    async def login_to_facebook(self, email: str, password: str) -> bool:
        """
        Login to Facebook

        Args:
            email: Facebook email/username
            password: Facebook password

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            logger.info("Navigating to Facebook login page")
            await self.tab.go_to("https://www.facebook.com/login")

            # Handle Cloudflare protection if present
            async with self.tab.expect_and_bypass_cloudflare_captcha():
                await asyncio.sleep(2)

            # Wait for login form to load
            await asyncio.sleep(3)

            # Find and fill email field
            email_field = await self.tab.find(id="email", timeout=10)
            if not email_field:
                email_field = await self.tab.find(name="email", timeout=5)

            if email_field:
                await email_field.clear()
                await email_field.type_text(email, delay=0.1)
                logger.info("Email entered")
            else:
                logger.error("Could not find email field")
                return False

            # Find and fill password field
            password_field = await self.tab.find(id="pass", timeout=10)
            if not password_field:
                password_field = await self.tab.find(name="pass", timeout=5)

            if password_field:
                await password_field.clear()
                await password_field.type_text(password, delay=0.1)
                logger.info("Password entered")
            else:
                logger.error("Could not find password field")
                return False

            # Find and click login button
            login_button = await self.tab.find(name="login", timeout=10)
            if not login_button:
                login_button = await self.tab.find(tag_name="button", text="Log in", timeout=5)
            if not login_button:
                login_button = await self.tab.find(tag_name="button", text="Log In", timeout=5)

            if login_button:
                await login_button.click()
                logger.info("Login button clicked")
            else:
                logger.error("Could not find login button")
                return False

            # Wait for login to complete and check if successful
            await asyncio.sleep(5)

            current_url = await self.tab.current_url
            if "facebook.com" in current_url and "login" not in current_url:
                self.logged_in = True
                logger.info("Login successful")
                return True
            else:
                logger.error("Login failed - still on login page")
                return False

        except Exception as e:
            logger.error(f"Login failed with error: {e}")
            return False

    async def search_users_by_location_and_name(
        self,
        location: str,
        last_name: str,
        max_results: int = 20
    ) -> List[FacebookUser]:
        """
        Search for Facebook users by location and last name

        Args:
            location: Location to search in
            last_name: Last name to search for
            max_results: Maximum number of results to return

        Returns:
            List of FacebookUser objects
        """
        if not self.logged_in:
            logger.error("Must be logged in to search users")
            return []

        users = []

        try:
            # Navigate to Facebook search
            search_query = f"people named {last_name} who live in {location}"
            search_url = f"https://www.facebook.com/search/people/?q={search_query.replace(' ', '%20')}"

            logger.info(f"Searching for: {search_query}")
            await self.tab.go_to(search_url)
            await asyncio.sleep(5)

            # Handle any popups or notifications
            await self._dismiss_popups()

            # Scroll to load more results
            for _ in range(3):
                await self.tab.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                await asyncio.sleep(2)

            # Find user profile elements
            profile_elements = await self.tab.find(
                tag_name="div",
                class_name="x1yztbdb",
                find_all=True,
                timeout=10
            )

            if not profile_elements:
                # Try alternative selectors
                profile_elements = await self.tab.find(
                    tag_name="div",
                    find_all=True,
                    timeout=5
                )
                # Filter for profile-like elements
                profile_elements = [elem for elem in profile_elements if await self._is_profile_element(elem)]

            logger.info(f"Found {len(profile_elements)} potential profile elements")

            for i, element in enumerate(profile_elements[:max_results]):
                try:
                    user = await self._extract_user_info(element, location)
                    if user and last_name.lower() in user.name.lower():
                        users.append(user)
                        logger.info(f"Extracted user: {user.name}")

                        if len(users) >= max_results:
                            break

                except Exception as e:
                    logger.warning(f"Failed to extract user info from element {i}: {e}")
                    continue

            logger.info(f"Successfully extracted {len(users)} users")
            return users

        except Exception as e:
            logger.error(f"Error searching users: {e}")
            return users

    async def _dismiss_popups(self):
        """Dismiss common Facebook popups and notifications"""
        try:
            # Try to close notification popup
            close_buttons = await self.tab.find(
                tag_name="div",
                aria_label="Close",
                find_all=True,
                timeout=3,
                raise_exc=False
            )

            for button in close_buttons or []:
                try:
                    await button.click()
                    await asyncio.sleep(1)
                except:
                    pass

            # Try to dismiss "Allow notifications" popup
            not_now_buttons = await self.tab.find(
                tag_name="button",
                text="Not Now",
                find_all=True,
                timeout=3,
                raise_exc=False
            )

            for button in not_now_buttons or []:
                try:
                    await button.click()
                    await asyncio.sleep(1)
                except:
                    pass

        except Exception as e:
            logger.debug(f"Error dismissing popups: {e}")

    async def _is_profile_element(self, element) -> bool:
        """Check if an element contains profile information"""
        try:
            # Look for profile indicators
            text_content = await element.get_text()
            if not text_content:
                return False

            # Check for profile-like patterns
            has_name = bool(re.search(r'[A-Z][a-z]+ [A-Z][a-z]+', text_content))
            has_profile_link = "profile" in text_content.lower() or "people" in text_content.lower()

            return has_name or has_profile_link

        except:
            return False

    async def _extract_user_info(self, element, location: str) -> Optional[FacebookUser]:
        """Extract user information from a profile element"""
        try:
            # Get text content
            text_content = await element.get_text()
            if not text_content:
                return None

            # Extract name (look for pattern like "First Last")
            name_match = re.search(r'([A-Z][a-z]+ [A-Z][a-z]+)', text_content)
            if not name_match:
                return None

            name = name_match.group(1)

            # Try to find profile link
            profile_url = None
            try:
                link_element = await element.find(tag_name="a", timeout=2, raise_exc=False)
                if link_element:
                    href = await link_element.get_attribute("href")
                    if href and "facebook.com" in href:
                        profile_url = href
            except:
                pass

            # Try to find profile picture
            profile_picture_url = None
            try:
                img_element = await element.find(tag_name="img", timeout=2, raise_exc=False)
                if img_element:
                    src = await img_element.get_attribute("src")
                    if src and "profile" in src.lower():
                        profile_picture_url = src
            except:
                pass

            # Extract bio/description if available
            bio = None
            if len(text_content) > len(name) + 10:
                bio = text_content.replace(name, "").strip()[:200]  # Limit bio length

            return FacebookUser(
                name=name,
                profile_url=profile_url or f"https://www.facebook.com/search/people/?q={name.replace(' ', '%20')}",
                location=location,
                profile_picture_url=profile_picture_url,
                bio=bio
            )

        except Exception as e:
            logger.debug(f"Error extracting user info: {e}")
            return None

    async def get_user_posts(self, user: FacebookUser, max_posts: int = 10) -> List[FacebookPost]:
        """
        Get recent posts from a user's profile

        Args:
            user: FacebookUser object
            max_posts: Maximum number of posts to retrieve

        Returns:
            List of FacebookPost objects
        """
        if not self.logged_in:
            logger.error("Must be logged in to get user posts")
            return []

        posts = []

        try:
            if not user.profile_url:
                logger.warning(f"No profile URL for user {user.name}")
                return []

            logger.info(f"Getting posts for user: {user.name}")
            await self.tab.go_to(user.profile_url)
            await asyncio.sleep(5)

            # Dismiss any popups
            await self._dismiss_popups()

            # Scroll to load posts
            for _ in range(3):
                await self.tab.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                await asyncio.sleep(2)

            # Find post elements
            post_elements = await self.tab.find(
                tag_name="div",
                find_all=True,
                timeout=10
            )

            # Filter for actual post elements
            post_elements = [elem for elem in post_elements if await self._is_post_element(elem)]

            logger.info(f"Found {len(post_elements)} potential post elements")

            for i, element in enumerate(post_elements[:max_posts]):
                try:
                    post = await self._extract_post_info(element, user)
                    if post:
                        posts.append(post)
                        logger.info(f"Extracted post {i+1} for {user.name}")

                except Exception as e:
                    logger.warning(f"Failed to extract post {i}: {e}")
                    continue

            logger.info(f"Successfully extracted {len(posts)} posts for {user.name}")
            return posts

        except Exception as e:
            logger.error(f"Error getting posts for {user.name}: {e}")
            return posts

    async def _is_post_element(self, element) -> bool:
        """Check if an element contains a post"""
        try:
            text_content = await element.get_text()
            if not text_content or len(text_content) < 10:
                return False

            # Look for post indicators
            has_timestamp = bool(re.search(r'\d+[hmd]|\d+ (hour|minute|day|week|month)', text_content.lower()))
            has_content = len(text_content.split()) > 5

            return has_timestamp and has_content

        except:
            return False

    async def _extract_post_info(self, element, user: FacebookUser) -> Optional[FacebookPost]:
        """Extract post information from a post element"""
        try:
            # Get post text content
            text_content = await element.get_text()
            if not text_content or len(text_content) < 10:
                return None

            # Extract timestamp
            timestamp = "Unknown"
            timestamp_match = re.search(r'(\d+[hmd]|\d+ (hour|minute|day|week|month))', text_content.lower())
            if timestamp_match:
                timestamp = timestamp_match.group(1)

            # Extract main content (remove timestamp and other metadata)
            content = text_content
            for pattern in [r'\d+[hmd]', r'\d+ (hour|minute|day|week|month)', r'Like', r'Comment', r'Share']:
                content = re.sub(pattern, '', content, flags=re.IGNORECASE)
            content = content.strip()

            # Find images in the post
            images = []
            try:
                img_elements = await element.find(tag_name="img", find_all=True, timeout=2, raise_exc=False)
                for img in img_elements or []:
                    src = await img.get_attribute("src")
                    if src and "scontent" in src:  # Facebook image URLs contain 'scontent'
                        images.append(src)
            except:
                pass

            # Generate post ID (simplified)
            post_id = f"{user.name}_{hash(content[:50])}_{int(time.time())}"

            return FacebookPost(
                user=user,
                post_id=post_id,
                content=content,
                timestamp=timestamp,
                images=images,
                post_url=user.profile_url
            )

        except Exception as e:
            logger.debug(f"Error extracting post info: {e}")
            return None

    async def analyze_posts_for_travel(self, posts: List[FacebookPost]) -> List[tuple[FacebookPost, TravelAnalysis]]:
        """
        Analyze posts for travel-related content using Gemini AI

        Args:
            posts: List of FacebookPost objects to analyze

        Returns:
            List of tuples containing (post, travel_analysis)
        """
        results = []

        for post in posts:
            try:
                logger.info(f"Analyzing post: {post.post_id}")

                # Analyze text content
                text_analysis = await self.gemini_analyzer.analyze_text_for_travel(post.content)

                # If post has images, analyze them too
                image_analyses = []
                for image_url in post.images:
                    try:
                        # Download image
                        image_data = await self._download_image(image_url)
                        if image_data:
                            image_analysis = await self.gemini_analyzer.analyze_image_for_travel(image_data)
                            image_analyses.append(image_analysis)
                    except Exception as e:
                        logger.warning(f"Failed to analyze image {image_url}: {e}")

                # Combine text and image analysis
                combined_analysis = self._combine_analyses(text_analysis, image_analyses)

                if combined_analysis.is_travel_related:
                    results.append((post, combined_analysis))
                    logger.info(f"Travel content detected in post {post.post_id} (confidence: {combined_analysis.confidence_score:.2f})")

            except Exception as e:
                logger.error(f"Error analyzing post {post.post_id}: {e}")
                continue

        return results

    async def _download_image(self, image_url: str) -> Optional[bytes]:
        """Download image from URL"""
        try:
            # Use browser to download image
            response = await self.tab.execute_script(f"""
                return fetch('{image_url}')
                    .then(response => response.arrayBuffer())
                    .then(buffer => Array.from(new Uint8Array(buffer)));
            """)

            if response and isinstance(response, list):
                return bytes(response)

        except Exception as e:
            logger.debug(f"Error downloading image {image_url}: {e}")

        return None

    def _combine_analyses(self, text_analysis: TravelAnalysis, image_analyses: List[TravelAnalysis]) -> TravelAnalysis:
        """Combine text and image analyses into a single result"""

        # Start with text analysis
        combined = TravelAnalysis(
            is_travel_related=text_analysis.is_travel_related,
            confidence_score=text_analysis.confidence_score,
            travel_keywords=text_analysis.travel_keywords.copy(),
            destinations_mentioned=text_analysis.destinations_mentioned.copy(),
            travel_type=text_analysis.travel_type,
            analysis_summary=text_analysis.analysis_summary
        )

        # Incorporate image analyses
        for img_analysis in image_analyses:
            if img_analysis.is_travel_related:
                combined.is_travel_related = True
                combined.confidence_score = max(combined.confidence_score, img_analysis.confidence_score)
                combined.travel_keywords.extend(img_analysis.travel_keywords)
                combined.destinations_mentioned.extend(img_analysis.destinations_mentioned)

                if not combined.travel_type and img_analysis.travel_type:
                    combined.travel_type = img_analysis.travel_type

                combined.analysis_summary += f" | Image: {img_analysis.analysis_summary}"

        # Remove duplicates
        combined.travel_keywords = list(set(combined.travel_keywords))
        combined.destinations_mentioned = list(set(combined.destinations_mentioned))

        return combined

    async def search_and_analyze(
        self,
        location: str,
        last_name: str,
        max_users: int = 10,
        max_posts_per_user: int = 5
    ) -> Dict[str, Any]:
        """
        Complete search and analysis workflow

        Args:
            location: Location to search in
            last_name: Last name to search for
            max_users: Maximum number of users to analyze
            max_posts_per_user: Maximum posts per user to analyze

        Returns:
            Dictionary containing all results
        """
        results = {
            "search_params": {
                "location": location,
                "last_name": last_name,
                "max_users": max_users,
                "max_posts_per_user": max_posts_per_user
            },
            "users_found": [],
            "travel_posts": [],
            "summary": {
                "total_users": 0,
                "total_posts_analyzed": 0,
                "travel_posts_found": 0,
                "top_destinations": [],
                "travel_types": {}
            }
        }

        try:
            # Search for users
            logger.info(f"Searching for users with last name '{last_name}' in '{location}'")
            users = await self.search_users_by_location_and_name(location, last_name, max_users)
            results["users_found"] = [asdict(user) for user in users]
            results["summary"]["total_users"] = len(users)

            if not users:
                logger.warning("No users found")
                return results

            # Analyze posts for each user
            all_travel_posts = []
            total_posts = 0

            for user in users:
                logger.info(f"Analyzing posts for user: {user.name}")
                posts = await self.get_user_posts(user, max_posts_per_user)
                total_posts += len(posts)

                if posts:
                    travel_results = await self.analyze_posts_for_travel(posts)
                    all_travel_posts.extend(travel_results)

            results["summary"]["total_posts_analyzed"] = total_posts
            results["summary"]["travel_posts_found"] = len(all_travel_posts)

            # Process travel posts
            destinations = []
            travel_types = {}

            for post, analysis in all_travel_posts:
                post_data = asdict(post)
                post_data["travel_analysis"] = asdict(analysis)
                results["travel_posts"].append(post_data)

                destinations.extend(analysis.destinations_mentioned)

                if analysis.travel_type:
                    travel_types[analysis.travel_type] = travel_types.get(analysis.travel_type, 0) + 1

            # Generate summary statistics
            from collections import Counter
            destination_counts = Counter(destinations)
            results["summary"]["top_destinations"] = [
                {"destination": dest, "mentions": count}
                for dest, count in destination_counts.most_common(10)
            ]
            results["summary"]["travel_types"] = travel_types

            logger.info(f"Analysis complete: {len(all_travel_posts)} travel posts found")
            return results

        except Exception as e:
            logger.error(f"Error in search and analysis: {e}")
            results["error"] = str(e)
            return results


async def main():
    """
    Main execution function - example usage
    """
    # Configuration
    FACEBOOK_EMAIL = os.getenv("FACEBOOK_EMAIL")
    FACEBOOK_PASSWORD = os.getenv("FACEBOOK_PASSWORD")
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

    if not all([FACEBOOK_EMAIL, FACEBOOK_PASSWORD, GEMINI_API_KEY]):
        print("Please set the following environment variables:")
        print("- FACEBOOK_EMAIL: Your Facebook email")
        print("- FACEBOOK_PASSWORD: Your Facebook password")
        print("- GEMINI_API_KEY: Your Gemini API key")
        return

    # Search parameters
    location = "Malaysia"  # Change this to your target location
    last_name = "Smith"    # Change this to your target last name

    async with FacebookScraper(gemini_api_key=GEMINI_API_KEY, headless=False) as scraper:
        # Login to Facebook
        print("Logging in to Facebook...")
        login_success = await scraper.login_to_facebook(FACEBOOK_EMAIL, FACEBOOK_PASSWORD)

        if not login_success:
            print("Failed to login to Facebook")
            return

        print("Login successful!")

        # Perform search and analysis
        print(f"Searching for users with last name '{last_name}' in '{location}'...")
        results = await scraper.search_and_analyze(
            location=location,
            last_name=last_name,
            max_users=5,
            max_posts_per_user=10
        )

        # Save results to file
        output_file = f"facebook_travel_analysis_{location}_{last_name}_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"\nResults saved to: {output_file}")

        # Print summary
        summary = results["summary"]
        print(f"\n=== ANALYSIS SUMMARY ===")
        print(f"Users found: {summary['total_users']}")
        print(f"Posts analyzed: {summary['total_posts_analyzed']}")
        print(f"Travel posts found: {summary['travel_posts_found']}")

        if summary["top_destinations"]:
            print(f"\nTop destinations mentioned:")
            for dest in summary["top_destinations"][:5]:
                print(f"  - {dest['destination']}: {dest['mentions']} mentions")

        if summary["travel_types"]:
            print(f"\nTravel types found:")
            for travel_type, count in summary["travel_types"].items():
                print(f"  - {travel_type}: {count} posts")

        # Print some example travel posts
        if results["travel_posts"]:
            print(f"\n=== EXAMPLE TRAVEL POSTS ===")
            for i, post_data in enumerate(results["travel_posts"][:3]):
                print(f"\nPost {i+1}:")
                print(f"User: {post_data['user']['name']}")
                print(f"Content: {post_data['content'][:200]}...")
                print(f"Confidence: {post_data['travel_analysis']['confidence_score']:.2f}")
                print(f"Destinations: {', '.join(post_data['travel_analysis']['destinations_mentioned'])}")


def save_results_to_csv(results: Dict[str, Any], filename: str):
    """
    Save travel analysis results to CSV format

    Args:
        results: Results dictionary from search_and_analyze
        filename: Output CSV filename
    """
    import csv

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'user_name', 'user_location', 'post_content', 'timestamp',
            'is_travel_related', 'confidence_score', 'travel_keywords',
            'destinations_mentioned', 'travel_type', 'analysis_summary'
        ]

        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for post_data in results.get("travel_posts", []):
            row = {
                'user_name': post_data['user']['name'],
                'user_location': post_data['user']['location'],
                'post_content': post_data['content'][:500],  # Limit content length
                'timestamp': post_data['timestamp'],
                'is_travel_related': post_data['travel_analysis']['is_travel_related'],
                'confidence_score': post_data['travel_analysis']['confidence_score'],
                'travel_keywords': ', '.join(post_data['travel_analysis']['travel_keywords']),
                'destinations_mentioned': ', '.join(post_data['travel_analysis']['destinations_mentioned']),
                'travel_type': post_data['travel_analysis']['travel_type'],
                'analysis_summary': post_data['travel_analysis']['analysis_summary']
            }
            writer.writerow(row)


if __name__ == "__main__":
    # Example usage
    print("Facebook Tour Scraper with Gemini AI Analysis")
    print("=" * 50)

    # Run the main function
    asyncio.run(main())
