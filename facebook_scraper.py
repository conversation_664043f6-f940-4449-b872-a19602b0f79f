"""
Facebook Tour Scraper using pyDoll and Gemini LLM

This scraper searches for Facebook users by location and last name,
then analyzes their posts for travel/tour-related content using Gemini AI.
"""

import asyncio
import base64
import json
import logging
import os
import re
import time
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Any
from google import genai
from google.genai import types
from pydoll.browser import Chrome
from pydoll.browser.options import ChromiumOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class FacebookUser:
    """Data class for Facebook user information"""
    name: str
    profile_url: str
    location: str
    profile_picture_url: Optional[str] = None
    mutual_friends: int = 0
    bio: Optional[str] = None


@dataclass
class FacebookPost:
    """Data class for Facebook post information"""
    user: FacebookUser
    post_id: str
    content: str
    timestamp: str
    images: List[str]
    likes: int = 0
    comments: int = 0
    shares: int = 0
    post_url: Optional[str] = None


@dataclass
class TravelAnalysis:
    """Data class for travel analysis results"""
    is_travel_related: bool
    confidence_score: float
    travel_keywords: List[str]
    destinations_mentioned: List[str]
    travel_type: Optional[str] = None  # vacation, business, tour, etc.
    analysis_summary: str = ""


class GeminiAnalyzer:
    """Handles travel content analysis using Gemini LLM"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.environ.get("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        self.client = genai.Client(api_key=self.api_key)
        self.model = "gemini-2.5-flash-preview-04-17"
    
    async def analyze_text_for_travel(self, text: str) -> TravelAnalysis:
        """Analyze text content for travel-related information"""
        
        prompt = f"""
        Analyze the following social media post text for travel-related content.
        
        Text to analyze: "{text}"
        
        Please provide a JSON response with the following structure:
        {{
            "is_travel_related": boolean,
            "confidence_score": float (0.0 to 1.0),
            "travel_keywords": [list of travel-related words found],
            "destinations_mentioned": [list of places/destinations mentioned],
            "travel_type": "vacation|business|tour|adventure|other|null",
            "analysis_summary": "brief explanation of the analysis"
        }}
        
        Look for indicators like:
        - Mentions of countries, cities, landmarks
        - Travel activities (sightseeing, touring, visiting)
        - Transportation (flight, hotel, airport)
        - Travel emotions (vacation, holiday, trip)
        - Location tags or check-ins
        """
        
        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=prompt)]
            )
        ]
        
        config = types.GenerateContentConfig(
            response_mime_type="application/json",
        )
        
        try:
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model,
                contents=contents,
                config=config
            )
            
            result = json.loads(response.text)
            return TravelAnalysis(**result)
            
        except Exception as e:
            logger.error(f"Error analyzing text with Gemini: {e}")
            return TravelAnalysis(
                is_travel_related=False,
                confidence_score=0.0,
                travel_keywords=[],
                destinations_mentioned=[],
                analysis_summary=f"Analysis failed: {str(e)}"
            )
    
    async def analyze_image_for_travel(self, image_data: bytes) -> TravelAnalysis:
        """Analyze image content for travel-related information"""
        
        image_b64 = base64.b64encode(image_data).decode('utf-8')
        
        prompt = """
        Analyze this image for travel-related content. Look for:
        - Tourist attractions, landmarks, monuments
        - Travel activities (sightseeing, tours, adventures)
        - Transportation (planes, trains, buses, boats)
        - Hotels, restaurants, vacation settings
        - Natural landscapes, beaches, mountains
        - Cultural sites, museums, historical places
        
        Provide a JSON response with the same structure as text analysis.
        """
        
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        mime_type="image/jpeg",
                        data=base64.b64decode(image_b64)
                    ),
                    types.Part.from_text(text=prompt)
                ]
            )
        ]
        
        config = types.GenerateContentConfig(
            response_mime_type="application/json",
        )
        
        try:
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model,
                contents=contents,
                config=config
            )
            
            result = json.loads(response.text)
            return TravelAnalysis(**result)
            
        except Exception as e:
            logger.error(f"Error analyzing image with Gemini: {e}")
            return TravelAnalysis(
                is_travel_related=False,
                confidence_score=0.0,
                travel_keywords=[],
                destinations_mentioned=[],
                analysis_summary=f"Image analysis failed: {str(e)}"
            )

    async def analyze_text_for_custom_criteria(self, text: str, criteria: str) -> TravelAnalysis:
        """Analyze text content for custom criteria"""

        prompt = f"""
        Analyze the following social media post text to see if it matches the specific criteria.

        Text to analyze: "{text}"

        Criteria to match: "{criteria}"

        Please provide a JSON response with the following structure:
        {{
            "is_travel_related": boolean (true if the post matches the criteria),
            "confidence_score": float (0.0 to 1.0, how confident you are in the match),
            "travel_keywords": [list of relevant keywords found],
            "destinations_mentioned": [list of places/destinations mentioned],
            "travel_type": "vacation|business|tour|adventure|other|null",
            "analysis_summary": "brief explanation of why this does or doesn't match the criteria"
        }}

        Be specific about whether the post content matches the given criteria. Look for:
        - Direct mentions of the criteria
        - Related activities or experiences
        - Location mentions that match the criteria
        - Context that suggests the criteria is met
        """

        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=prompt)]
            )
        ]

        config = types.GenerateContentConfig(
            response_mime_type="application/json",
        )

        try:
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model,
                contents=contents,
                config=config
            )

            result = json.loads(response.text)
            return TravelAnalysis(**result)

        except Exception as e:
            logger.error(f"Error analyzing text with Gemini: {e}")
            return TravelAnalysis(
                is_travel_related=False,
                confidence_score=0.0,
                travel_keywords=[],
                destinations_mentioned=[],
                analysis_summary=f"Analysis failed: {str(e)}"
            )

    async def analyze_image_for_custom_criteria(self, image_data: bytes, criteria: str) -> TravelAnalysis:
        """Analyze image content for custom criteria"""

        image_b64 = base64.b64encode(image_data).decode('utf-8')

        prompt = f"""
        Analyze this image to see if it matches the specific criteria: "{criteria}"

        Look for visual elements that match or relate to the criteria such as:
        - Locations mentioned in the criteria
        - Activities described in the criteria
        - Objects, landmarks, or scenes related to the criteria
        - People engaged in activities matching the criteria

        Provide a JSON response with the same structure as text analysis.
        """

        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        mime_type="image/jpeg",
                        data=base64.b64decode(image_b64)
                    ),
                    types.Part.from_text(text=prompt)
                ]
            )
        ]

        config = types.GenerateContentConfig(
            response_mime_type="application/json",
        )

        try:
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model,
                contents=contents,
                config=config
            )

            result = json.loads(response.text)
            return TravelAnalysis(**result)

        except Exception as e:
            logger.error(f"Error analyzing image with Gemini: {e}")
            return TravelAnalysis(
                is_travel_related=False,
                confidence_score=0.0,
                travel_keywords=[],
                destinations_mentioned=[],
                analysis_summary=f"Image analysis failed: {str(e)}"
            )


class FacebookScraper:
    """Main Facebook scraper class using pyDoll"""

    def __init__(self, gemini_api_key: Optional[str] = None, headless: bool = False):
        self.gemini_analyzer = GeminiAnalyzer(gemini_api_key)
        self.headless = headless
        self.browser = None
        self.tab = None
        self.logged_in = False
        self.should_continue = True  # Control flag for manual stopping
        self.pause_requested = False  # Control flag for pausing
        
    async def __aenter__(self):
        await self.start_browser()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close_browser()
    
    async def start_browser(self):
        """Initialize and start the browser"""
        options = ChromiumOptions()
        
        if self.headless:
            options.add_argument('--headless')
        
        # Add user agent to appear more human-like
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Additional options for better compatibility
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        self.browser = Chrome(options=options)
        self.tab = await self.browser.start(headless=self.headless)
        
        # Enable necessary events
        await self.tab.enable_page_events()
        await self.tab.enable_network_events()
        
        logger.info("Browser started successfully")
    
    async def close_browser(self):
        """Close the browser"""
        if self.browser:
            try:
                await self.browser.stop()
                logger.info("Browser closed")
            except Exception as e:
                logger.warning(f"Error closing browser: {e}")
                # Try to force close if normal close fails
                try:
                    await self.browser._browser_process_manager.stop_process()
                except:
                    pass

    def stop_scraper(self):
        """Stop the scraper manually"""
        self.should_continue = False
        logger.info("🛑 Scraper stop requested by user")

    def pause_scraper(self):
        """Pause the scraper"""
        self.pause_requested = True
        logger.info("⏸️ Scraper pause requested by user")

    def resume_scraper(self):
        """Resume the scraper"""
        self.pause_requested = False
        logger.info("▶️ Scraper resumed by user")

    async def _check_control_flags(self):
        """Check if user wants to pause or stop"""
        if not self.should_continue:
            logger.info("🛑 Scraper stopped by user")
            return False

        while self.pause_requested:
            logger.info("⏸️ Scraper paused. Call resume_scraper() to continue...")
            await asyncio.sleep(2)

        return True

    def wait_for_user_input(self, message: str = "Press Enter to continue or 'q' to quit: "):
        """Wait for user input to continue or quit"""
        try:
            user_input = input(message).strip().lower()
            if user_input in ['q', 'quit', 'stop']:
                self.stop_scraper()
                return False
            return True
        except KeyboardInterrupt:
            self.stop_scraper()
            return False

    async def login_to_facebook_manual(self, timeout: int = 600) -> bool:
        """
        Navigate to Facebook login page and intelligently wait for manual login

        Args:
            timeout: Maximum seconds to wait for manual login (default 600 = 10 minutes)

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            logger.info("Navigating to Facebook login page")
            await self.tab.go_to("https://www.facebook.com/login")

            # Handle Cloudflare protection if present
            try:
                async with self.tab.expect_and_bypass_cloudflare_captcha():
                    pass
            except:
                pass

            print("\n" + "="*60)
            print("🔐 MANUAL LOGIN REQUIRED")
            print("="*60)
            print("Please login to Facebook manually in the browser window.")
            print("The scraper will intelligently detect when you're logged in.")
            print("Handle any 2FA, captcha, or security checks as needed.")
            print(f"Timeout: {timeout} seconds ({timeout//60} minutes)")
            print("="*60)

            # Intelligent login detection
            start_time = time.time()
            last_url = ""
            consecutive_same_url = 0

            while time.time() - start_time < timeout:
                try:
                    # Get current URL and check for login status
                    current_url = await self.tab.current_url

                    # Track URL changes to detect navigation
                    if current_url == last_url:
                        consecutive_same_url += 1
                    else:
                        consecutive_same_url = 0
                        last_url = current_url
                        logger.debug(f"URL changed to: {current_url}")

                    # Multiple checks for successful login
                    login_indicators = await self._check_login_status(current_url)

                    if login_indicators["is_logged_in"]:
                        self.logged_in = True
                        print(f"\n✅ Login detected successfully!")
                        print(f"   Status: {login_indicators['status']}")
                        logger.info(f"Manual login successful: {login_indicators['status']}")
                        return True

                    # Handle special cases
                    if login_indicators["needs_attention"]:
                        print(f"\n⚠️  {login_indicators['message']}")
                        print("   Please complete the required action in the browser.")

                    # Show progress (less frequent updates)
                    elapsed = int(time.time() - start_time)
                    remaining = timeout - elapsed

                    if elapsed % 10 == 0:  # Update every 10 seconds
                        status_msg = login_indicators.get('status', 'Waiting for login')
                        print(f"\r⏳ {status_msg}... {elapsed}s elapsed, {remaining}s remaining", end="", flush=True)

                    # Adaptive check interval based on activity
                    if consecutive_same_url > 10:
                        check_interval = 3  # Slower checks if no activity
                    else:
                        check_interval = 1  # Faster checks during activity

                    await asyncio.sleep(check_interval)

                except Exception as e:
                    logger.debug(f"Error during login check: {e}")
                    await asyncio.sleep(2)

            print(f"\n❌ Login timeout after {timeout} seconds")
            logger.error("Manual login timeout")
            return False

        except Exception as e:
            logger.error(f"Manual login failed with error: {e}")
            return False

    async def _check_login_status(self, current_url: str) -> dict:
        """
        Intelligently check if user is logged in to Facebook

        Returns:
            dict: Status information about login state
        """
        result = {
            "is_logged_in": False,
            "needs_attention": False,
            "status": "Checking login status",
            "message": ""
        }

        try:
            # URL-based checks
            if "login" in current_url:
                result["status"] = "On login page"
                return result

            if "checkpoint" in current_url:
                result["needs_attention"] = True
                result["status"] = "Security checkpoint"
                result["message"] = "Facebook security checkpoint detected. Please complete verification."
                return result

            if "two_factor" in current_url or "2fa" in current_url:
                result["needs_attention"] = True
                result["status"] = "Two-factor authentication"
                result["message"] = "Two-factor authentication required. Please enter your code."
                return result

            if "captcha" in current_url:
                result["needs_attention"] = True
                result["status"] = "Captcha verification"
                result["message"] = "Captcha verification required. Please complete the captcha."
                return result

            # If we're on facebook.com but not login/checkpoint pages
            if "facebook.com" in current_url:
                # Try multiple methods to confirm login
                login_confirmed = await self._confirm_facebook_login()

                if login_confirmed:
                    result["is_logged_in"] = True
                    result["status"] = "Successfully logged in"
                    return result
                else:
                    result["status"] = "Verifying login status"
                    return result

            result["status"] = "Navigating to Facebook"
            return result

        except Exception as e:
            logger.debug(f"Error checking login status: {e}")
            result["status"] = "Error checking status"
            return result

    async def _confirm_facebook_login(self) -> bool:
        """
        Confirm that user is actually logged in using multiple checks

        Returns:
            bool: True if definitely logged in, False otherwise
        """
        try:
            # Method 1: Look for user profile menu/avatar
            profile_indicators = [
                {"tag_name": "div", "aria_label": "Account"},
                {"tag_name": "div", "aria_label": "Profile"},
                {"tag_name": "a", "aria_label": "Profile"},
                {"tag_name": "img", "alt": "Profile picture"},
            ]

            for indicator in profile_indicators:
                try:
                    element = await self.tab.find(timeout=2, raise_exc=False, **indicator)
                    if element:
                        logger.debug(f"Found login indicator: {indicator}")
                        return True
                except:
                    continue

            # Method 2: Look for navigation elements that only appear when logged in
            nav_indicators = [
                "Home", "Watch", "Marketplace", "Groups", "Gaming"
            ]

            for nav_text in nav_indicators:
                try:
                    nav_element = await self.tab.find(
                        tag_name="a",
                        text=nav_text,
                        timeout=1,
                        raise_exc=False
                    )
                    if nav_element:
                        logger.debug(f"Found navigation indicator: {nav_text}")
                        return True
                except:
                    continue

            # Method 3: Check for "What's on your mind" or posting interface
            posting_indicators = [
                "What's on your mind",
                "Write something",
                "Create post"
            ]

            for post_text in posting_indicators:
                try:
                    post_element = await self.tab.find(
                        tag_name="div",
                        text=post_text,
                        timeout=1,
                        raise_exc=False
                    )
                    if post_element:
                        logger.debug(f"Found posting indicator: {post_text}")
                        return True
                except:
                    continue

            # Method 4: Check page title
            try:
                title = await self.tab.execute_script("return document.title;")
                if title and "Facebook" in title and "Log In" not in title:
                    logger.debug(f"Page title indicates login: {title}")
                    return True
            except:
                pass

            return False

        except Exception as e:
            logger.debug(f"Error confirming login: {e}")
            return False

    async def login_to_facebook_auto(self, email: str, password: str) -> bool:
        """
        Automatic login to Facebook (legacy method)

        Args:
            email: Facebook email/username
            password: Facebook password

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            logger.info("Navigating to Facebook login page")
            await self.tab.go_to("https://www.facebook.com/login")

            # Handle Cloudflare protection if present
            async with self.tab.expect_and_bypass_cloudflare_captcha():
                await asyncio.sleep(2)

            # Wait for login form to load
            await asyncio.sleep(3)

            # Find and fill email field
            email_field = await self.tab.find(id="email", timeout=10)
            if not email_field:
                email_field = await self.tab.find(name="email", timeout=5)

            if email_field:
                # Focus the field first
                await email_field.click()
                await email_field.type_text(email, interval=0.1)
                logger.info("Email entered")
            else:
                logger.error("Could not find email field")
                return False

            # Find and fill password field
            password_field = await self.tab.find(id="pass", timeout=10)
            if not password_field:
                password_field = await self.tab.find(name="pass", timeout=5)

            if password_field:
                # Focus the field first
                await password_field.click()
                await password_field.type_text(password, interval=0.1)
                logger.info("Password entered")
            else:
                logger.error("Could not find password field")
                return False

            # Find and click login button
            login_button = await self.tab.find(name="login", timeout=10)
            if not login_button:
                login_button = await self.tab.find(tag_name="button", text="Log in", timeout=5)
            if not login_button:
                login_button = await self.tab.find(tag_name="button", text="Log In", timeout=5)

            if login_button:
                await login_button.click()
                logger.info("Login button clicked")
            else:
                logger.error("Could not find login button")
                return False

            # Wait for login to complete and check if successful
            await asyncio.sleep(5)

            current_url = await self.tab.current_url
            if "facebook.com" in current_url and "login" not in current_url:
                self.logged_in = True
                logger.info("Automatic login successful")
                return True
            else:
                logger.error("Automatic login failed - still on login page")
                return False

        except Exception as e:
            logger.error(f"Automatic login failed with error: {e}")
            return False

    async def search_users_by_exact_string(
        self,
        search_string: str,
        max_results: int = 20
    ) -> List[FacebookUser]:
        """
        Search for Facebook users using the exact search string provided

        Args:
            search_string: Exact string to search for (e.g., "John Smith", "Smith", etc.)
            max_results: Maximum number of results to return

        Returns:
            List of FacebookUser objects with profile URLs
        """
        if not self.logged_in:
            logger.error("Must be logged in to search users")
            return []

        users = []

        try:
            logger.info(f"🔍 Searching for exact string: '{search_string}'")
            print(f"🔍 Searching for exact string: '{search_string}'")

            # Go to Facebook main page first
            await self.tab.go_to("https://www.facebook.com")
            await asyncio.sleep(3)

            # Dismiss any popups
            await self._dismiss_popups()

            # Find and use the search box with exact string
            search_success = await self._perform_exact_facebook_search(search_string)

            if not search_success:
                logger.warning("Failed to use search box, trying direct URL method")
                # Fallback to direct URL method with exact string
                search_url = f"https://www.facebook.com/search/people/?q={search_string.replace(' ', '%20')}"
                logger.info(f"Using direct search URL: {search_url}")
                await self.tab.go_to(search_url)
                await asyncio.sleep(5)

            # Wait for search results to load
            await asyncio.sleep(5)

            # Check if we're on a search results page
            current_url = await self.tab.current_url
            if "search" not in current_url:
                logger.error("Not on search results page, something went wrong")
                print("❌ Search failed - not on search results page")
                print(f"Current URL: {current_url}")
                return []

            print("✅ Search completed, looking for profile results...")

            # Scroll to load more results
            for scroll in range(3):
                await self.tab.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                await asyncio.sleep(2)
                logger.info(f"Scrolled {scroll + 1}/3 times to load more profiles")

            # Extract profile information from search results
            users = await self._extract_profiles_from_search_results(search_string, max_results)

            logger.info(f"✅ Successfully found {len(users)} user profiles")
            print(f"✅ Found {len(users)} profiles for '{search_string}'")

            return users

        except Exception as e:
            logger.error(f"Error searching users: {e}")
            print(f"❌ Error during search: {e}")
            return users

    async def _perform_exact_facebook_search(self, search_string: str) -> bool:
        """
        Use Facebook's search box to search for the exact string

        Args:
            search_string: Exact string to search for

        Returns:
            bool: True if search was successful, False otherwise
        """
        try:
            # Look for search input field
            search_selectors = [
                {"tag_name": "input", "placeholder": "Search Facebook"},
                {"tag_name": "input", "aria_label": "Search Facebook"},
                {"tag_name": "input", "type": "search"},
                {"tag_name": "input", "name": "q"}
            ]

            search_input = None
            for selector in search_selectors:
                try:
                    search_input = await self.tab.find(timeout=3, raise_exc=False, **selector)
                    if search_input:
                        logger.info(f"Found search input with selector: {selector}")
                        break
                except:
                    continue

            if not search_input:
                logger.warning("Could not find search input field")
                return False

            # Click on search input and clear any existing text
            await search_input.click()
            await asyncio.sleep(1)

            # Clear the field first
            await self.tab.execute_script("""
                arguments[0].value = '';
                arguments[0].focus();
            """, search_input)

            # Type the exact search string
            logger.info(f"Typing exact search string: '{search_string}'")
            print(f"   Typing: '{search_string}'")
            await search_input.type_text(search_string, interval=0.1)
            await asyncio.sleep(2)

            # Press Enter to search
            await self.tab.execute_script("""
                var event = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true
                });
                arguments[0].dispatchEvent(event);
            """, search_input)

            await asyncio.sleep(5)

            # Check if we're on search results
            current_url = await self.tab.current_url
            if "search" in current_url:
                logger.info("Successfully navigated to search results")
                print(f"   ✅ Search successful, on results page")
                return True
            else:
                logger.warning(f"Search may have failed, current URL: {current_url}")
                print(f"   ❌ Search failed, current URL: {current_url}")
                return False

        except Exception as e:
            logger.error(f"Error performing Facebook search: {e}")
            print(f"   ❌ Search error: {e}")
            return False

    async def _extract_profiles_from_search_results(self, search_string: str, max_results: int) -> List[FacebookUser]:
        """
        Extract profile information from Facebook search results page

        Args:
            search_string: The search string we used
            max_results: Maximum number of profiles to extract

        Returns:
            List of FacebookUser objects
        """
        users = []

        try:
            print("🔍 Analyzing search results page...")

            # Get all links on the page
            all_links = await self.tab.find(tag_name="a", find_all=True, timeout=10)
            logger.info(f"Found {len(all_links)} total links on search results page")
            print(f"   Found {len(all_links)} total links")

            processed_urls = set()
            potential_profiles = []

            # Look through all links for profile URLs
            for link in all_links:
                try:
                    href = link.get_attribute("href")
                    if not href or href in processed_urls:
                        continue

                    processed_urls.add(href)

                    # Check if it's a valid profile URL
                    if self._is_valid_profile_url(href):
                        # Try to get the name/text associated with this link
                        link_text = await link.text

                        if link_text and link_text.strip():
                            # Clean up the text
                            cleaned_text = self._clean_extracted_name(link_text)

                            if cleaned_text:
                                potential_profiles.append({
                                    'name': cleaned_text,
                                    'url': href,
                                    'text': link_text
                                })
                                logger.debug(f"Found potential profile: {cleaned_text} - {href}")

                except Exception as e:
                    logger.debug(f"Error processing link: {e}")
                    continue

            logger.info(f"Found {len(potential_profiles)} potential profiles")
            print(f"   Found {len(potential_profiles)} potential profiles")

            # If we found profiles, create FacebookUser objects
            if potential_profiles:
                for profile in potential_profiles[:max_results]:
                    user = FacebookUser(
                        name=profile['name'],
                        profile_url=profile['url'],
                        location="Unknown"
                    )
                    users.append(user)
                    logger.info(f"✅ Added profile: {profile['name']}")
                    print(f"   👤 {profile['name']}")
                    print(f"      URL: {profile['url']}")

            # If we still don't have results, try a different approach
            if not users:
                print("   No direct profile links found, trying text analysis...")

                # Get page text and look for names
                page_text = await self.tab.execute_script("return document.body.innerText;")

                # Look for name patterns in the text
                name_patterns = re.findall(r'([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)', page_text)

                # Filter for relevant names
                relevant_names = []
                search_words = search_string.lower().split()

                for name in name_patterns:
                    name_words = name.lower().split()
                    # Check if any search word appears in the name
                    if any(search_word in name_word for search_word in search_words for name_word in name_words):
                        relevant_names.append(name)

                # Remove duplicates
                unique_names = list(dict.fromkeys(relevant_names))

                logger.info(f"Found {len(unique_names)} names in page text")
                print(f"   Found {len(unique_names)} names in page text")

                # Create users from names (with search URLs)
                for name in unique_names[:max_results]:
                    search_url = f"https://www.facebook.com/search/people/?q={name.replace(' ', '%20')}"
                    user = FacebookUser(
                        name=name,
                        profile_url=search_url,
                        location="Unknown"
                    )
                    users.append(user)
                    logger.info(f"📝 Added from text: {name}")
                    print(f"   📝 {name}")

            return users[:max_results]

        except Exception as e:
            logger.error(f"Error extracting profiles from search results: {e}")
            print(f"   ❌ Error extracting profiles: {e}")
            return users

    def _clean_extracted_name(self, text: str) -> str:
        """Clean and extract a proper name from link text"""
        if not text:
            return ""

        # Remove extra whitespace and newlines
        text = re.sub(r'\s+', ' ', text.strip())

        # Look for name pattern at the beginning
        name_match = re.match(r'^([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)', text)
        if name_match:
            return name_match.group(1)

        # If no pattern match, take first few words if they look like names
        words = text.split()
        if len(words) >= 2:
            potential_name = " ".join(words[:3])  # Take up to 3 words
            if self._looks_like_name(potential_name):
                return potential_name

        return text[:50]  # Fallback to first 50 characters

    def _is_valid_profile_url(self, url: str) -> bool:
        """Check if URL is a valid Facebook profile URL"""
        if not url or "facebook.com" not in url:
            return False

        # Valid profile URL patterns
        valid_patterns = [
            "/profile.php?id=",  # Numeric ID profiles
            "facebook.com/people/",  # People directory
        ]

        # Check for username profiles (facebook.com/username)
        if "facebook.com/" in url and not any(pattern in url for pattern in [
            "/pages/", "/groups/", "/events/", "/marketplace/", "/watch/",
            "/gaming/", "/search/", "/help/", "/privacy/", "/terms/"
        ]):
            # Extract the part after facebook.com/
            parts = url.split("facebook.com/")
            if len(parts) > 1:
                username_part = parts[1].split("/")[0].split("?")[0]
                # Valid username (not a system page)
                if username_part and not username_part.startswith(("www", "m", "mobile")):
                    return True

        return any(pattern in url for pattern in valid_patterns)

    async def _extract_name_from_link(self, link_element) -> str:
        """Extract name from profile link element"""
        try:
            # Try to get text from the link itself
            link_text = await link_element.text
            if link_text and len(link_text.split()) >= 2:
                return link_text.strip()

            # Try to find name in parent elements
            parent = link_element
            for _ in range(3):  # Check up to 3 parent levels
                try:
                    parent_text = await parent.text
                    if parent_text and len(parent_text.split()) >= 2:
                        # Extract what looks like a name (first few words)
                        words = parent_text.split()
                        if len(words) >= 2:
                            potential_name = " ".join(words[:3])  # Take first 3 words
                            if self._looks_like_name(potential_name):
                                return potential_name
                except:
                    pass

                # Try to go to parent (this might not work in pyDoll, but worth trying)
                try:
                    parent = await parent.find(tag_name="..", timeout=1, raise_exc=False)
                    if not parent:
                        break
                except:
                    break

            return "Unknown Name"

        except Exception as e:
            logger.debug(f"Error extracting name from link: {e}")
            return "Unknown Name"

    def _looks_like_name(self, text: str) -> bool:
        """Check if text looks like a person's name"""
        words = text.split()
        if len(words) < 2:
            return False

        # Check if words start with capital letters (basic name pattern)
        return all(word[0].isupper() and word[1:].islower() for word in words[:2] if word.isalpha())

    async def _dismiss_popups(self):
        """Dismiss common Facebook popups and notifications"""
        try:
            # Try to close notification popup
            close_buttons = await self.tab.find(
                tag_name="div",
                aria_label="Close",
                find_all=True,
                timeout=3,
                raise_exc=False
            )

            for button in close_buttons or []:
                try:
                    await button.click()
                    await asyncio.sleep(1)
                except:
                    pass

            # Try to dismiss "Allow notifications" popup
            not_now_buttons = await self.tab.find(
                tag_name="button",
                text="Not Now",
                find_all=True,
                timeout=3,
                raise_exc=False
            )

            for button in not_now_buttons or []:
                try:
                    await button.click()
                    await asyncio.sleep(1)
                except:
                    pass

        except Exception as e:
            logger.debug(f"Error dismissing popups: {e}")

    async def _is_profile_element(self, element) -> bool:
        """Check if an element contains profile information"""
        try:
            # Look for profile indicators
            text_content = await element.text
            if not text_content:
                return False

            # Check for profile-like patterns
            has_name = bool(re.search(r'[A-Z][a-z]+ [A-Z][a-z]+', text_content))
            has_profile_link = "profile" in text_content.lower() or "people" in text_content.lower()

            return has_name or has_profile_link

        except:
            return False

    async def _extract_user_info(self, element, location: str) -> Optional[FacebookUser]:
        """Extract user information from a profile element"""
        try:
            # Get text content
            text_content = await element.text
            if not text_content:
                return None

            # Extract name (look for pattern like "First Last")
            name_match = re.search(r'([A-Z][a-z]+ [A-Z][a-z]+)', text_content)
            if not name_match:
                return None

            name = name_match.group(1)

            # Try to find profile link
            profile_url = None
            try:
                link_element = await element.find(tag_name="a", timeout=2, raise_exc=False)
                if link_element:
                    href = link_element.get_attribute("href")
                    if href and "facebook.com" in href:
                        profile_url = href
            except:
                pass

            # Try to find profile picture
            profile_picture_url = None
            try:
                img_element = await element.find(tag_name="img", timeout=2, raise_exc=False)
                if img_element:
                    src = img_element.get_attribute("src")
                    if src and "profile" in src.lower():
                        profile_picture_url = src
            except:
                pass

            # Extract bio/description if available
            bio = None
            if len(text_content) > len(name) + 10:
                bio = text_content.replace(name, "").strip()[:200]  # Limit bio length

            return FacebookUser(
                name=name,
                profile_url=profile_url or f"https://www.facebook.com/search/people/?q={name.replace(' ', '%20')}",
                location=location,
                profile_picture_url=profile_picture_url,
                bio=bio
            )

        except Exception as e:
            logger.debug(f"Error extracting user info: {e}")
            return None

    async def get_user_posts(self, user: FacebookUser, max_posts: int = 10) -> List[FacebookPost]:
        """
        Get recent posts from a user's profile by carefully visiting their profile page

        Args:
            user: FacebookUser object
            max_posts: Maximum number of posts to retrieve

        Returns:
            List of FacebookPost objects
        """
        if not self.logged_in:
            logger.error("Must be logged in to get user posts")
            return []

        # Check control flags
        if not await self._check_control_flags():
            return []

        posts = []

        try:
            if not user.profile_url:
                logger.warning(f"No profile URL for user {user.name}")
                return []

            logger.info(f"📱 Visiting profile: {user.name}")
            print(f"\n📱 Visiting profile: {user.name}")
            print(f"🔗 URL: {user.profile_url}")

            # Navigate directly to user's profile with careful URL handling
            profile_url = await self._get_direct_profile_url(user)

            logger.info(f"🔗 Navigating to: {profile_url}")
            await self.tab.go_to(profile_url)

            # Wait for page to load and check if we're in the right place
            await asyncio.sleep(5)

            # Verify we're on the correct profile page
            current_url = await self.tab.current_url
            if not await self._verify_profile_page(current_url, user.name):
                logger.warning(f"❌ Not on correct profile page for {user.name}")
                print(f"❌ Failed to reach {user.name}'s profile")
                return []

            print(f"✅ Successfully reached {user.name}'s profile")

            # Dismiss any popups without clicking navigation elements
            await self._dismiss_popups_carefully()

            # Wait for user confirmation to continue
            if not self.wait_for_user_input("Press Enter to start scraping posts, or 'q' to skip this user: "):
                return []

            # Scroll carefully to load posts
            posts = await self._scrape_posts_from_profile(user, max_posts)

            logger.info(f"✅ Successfully extracted {len(posts)} posts for {user.name}")
            print(f"✅ Found {len(posts)} posts for {user.name}")

            return posts

        except Exception as e:
            logger.error(f"Error getting posts for {user.name}: {e}")
            print(f"❌ Error getting posts for {user.name}: {e}")
            return posts

    async def _get_direct_profile_url(self, user: FacebookUser) -> str:
        """
        Get a direct profile URL that's more likely to work

        Args:
            user: FacebookUser object

        Returns:
            str: Direct profile URL
        """
        original_url = user.profile_url

        # If it's already a direct profile URL, use it
        if "/profile.php?id=" in original_url or (
            "facebook.com/" in original_url and
            not any(x in original_url for x in ["/search/", "/pages/", "/groups/"])
        ):
            return original_url

        # If it's a search URL, try to extract the profile from it
        if "/search/" in original_url:
            # Create a direct search for this person
            name_for_search = user.name.replace(" ", "%20")
            return f"https://www.facebook.com/search/people/?q={name_for_search}"

        return original_url

    async def _verify_profile_page(self, current_url: str, expected_name: str) -> bool:
        """
        Verify we're on the correct profile page

        Args:
            current_url: Current page URL
            expected_name: Name we expect to see

        Returns:
            bool: True if we're on the right profile page
        """
        try:
            # Check URL patterns
            if any(pattern in current_url for pattern in [
                "profile_not_available", "content_not_found", "login", "checkpoint"
            ]):
                return False

            # Check if we're on Facebook
            if "facebook.com" not in current_url:
                return False

            # If we're on a search page, that's okay too
            if "/search/" in current_url:
                return True

            # Try to find the name on the page
            try:
                page_text = await self.tab.execute_script("return document.title + ' ' + document.body.innerText;")
                if expected_name.lower() in page_text.lower():
                    return True
            except:
                pass

            # If we're on facebook.com and not on obvious wrong pages, assume it's okay
            wrong_pages = ["/gaming/", "/watch/", "/marketplace/", "/groups/", "/pages/"]
            if not any(wrong in current_url for wrong in wrong_pages):
                return True

            return False

        except Exception as e:
            logger.debug(f"Error verifying profile page: {e}")
            return False

    async def _dismiss_popups_carefully(self):
        """Dismiss popups without clicking on navigation elements"""
        try:
            # Look for specific popup close buttons
            close_selectors = [
                {"tag_name": "div", "aria_label": "Close"},
                {"tag_name": "button", "aria_label": "Close"},
                {"tag_name": "div", "role": "button", "aria_label": "Close"},
            ]

            for selector in close_selectors:
                try:
                    close_button = await self.tab.find(timeout=2, raise_exc=False, **selector)
                    if close_button:
                        # Make sure it's actually a close button and not navigation
                        button_text = await close_button.text
                        if not button_text or len(button_text) < 10:  # Close buttons usually have short text
                            await close_button.click()
                            await asyncio.sleep(1)
                            logger.debug("Closed popup")
                except:
                    continue

            # Look for "Not Now" buttons for notifications
            try:
                not_now = await self.tab.find(
                    tag_name="button",
                    text="Not Now",
                    timeout=2,
                    raise_exc=False
                )
                if not_now:
                    await not_now.click()
                    await asyncio.sleep(1)
                    logger.debug("Clicked 'Not Now'")
            except:
                pass

        except Exception as e:
            logger.debug(f"Error dismissing popups: {e}")

    async def _scrape_posts_from_profile(self, user: FacebookUser, max_posts: int) -> List[FacebookPost]:
        """
        Scrape posts from the current profile page

        Args:
            user: FacebookUser object
            max_posts: Maximum posts to scrape

        Returns:
            List of FacebookPost objects
        """
        posts = []

        try:
            # Scroll to load posts
            logger.info(f"📜 Scrolling to load posts for {user.name}")
            print("📜 Scrolling to load posts...")

            for scroll_count in range(3):  # Reduced scrolling to avoid issues
                if not await self._check_control_flags():
                    return posts

                await self.tab.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                await asyncio.sleep(2)
                print(f"   Scroll {scroll_count + 1}/3 completed")

            # Get all text content from the page
            logger.info(f"🔍 Analyzing page content for posts")
            print("🔍 Analyzing page content for posts...")

            page_text = await self.tab.execute_script("return document.body.innerText;")

            # Split into potential post sections
            post_sections = self._extract_post_sections_from_text(page_text)

            logger.info(f"Found {len(post_sections)} potential post sections")
            print(f"📝 Found {len(post_sections)} potential post sections")

            # Process each section
            for i, section in enumerate(post_sections[:max_posts]):
                if not await self._check_control_flags():
                    break

                try:
                    if self._looks_like_post_content(section):
                        post = self._create_post_from_text(user, section, i)
                        if post:
                            posts.append(post)
                            print(f"📝 Post {len(posts)}: {section[:100]}...")

                except Exception as e:
                    logger.debug(f"Failed to process post section {i}: {e}")
                    continue

            return posts

        except Exception as e:
            logger.error(f"Error scraping posts from profile: {e}")
            return posts

    def _extract_post_sections_from_text(self, page_text: str) -> List[str]:
        """
        Extract potential post sections from page text

        Args:
            page_text: Full page text content

        Returns:
            List of potential post text sections
        """
        # Split by common separators and filter for post-like content
        sections = []

        # Split by multiple newlines (posts are usually separated)
        potential_sections = re.split(r'\n\s*\n', page_text)

        for section in potential_sections:
            section = section.strip()

            # Filter for sections that look like posts
            if (len(section) > 30 and  # Minimum length
                len(section) < 2000 and  # Maximum length
                not self._is_ui_element(section)):  # Not UI text
                sections.append(section)

        return sections

    def _is_ui_element(self, text: str) -> bool:
        """Check if text is likely a UI element rather than post content"""
        text_lower = text.lower()

        # Common UI elements to skip
        ui_patterns = [
            "home", "profile", "friends", "photos", "more", "about",
            "timeline", "posts", "reviews", "videos", "check-ins",
            "create post", "what's on your mind", "photo/video",
            "feeling/activity", "live video", "camera", "gif",
            "like", "comment", "share", "see more", "see less",
            "write a comment", "most relevant", "top comments",
            "view more comments", "gaming", "watch", "marketplace"
        ]

        # If text is mostly UI patterns, skip it
        ui_word_count = sum(1 for pattern in ui_patterns if pattern in text_lower)
        total_words = len(text.split())

        if total_words > 0 and ui_word_count / total_words > 0.3:
            return True

        return False

    def _create_post_from_text(self, user: FacebookUser, text: str, index: int) -> Optional[FacebookPost]:
        """
        Create a FacebookPost object from text content

        Args:
            user: FacebookUser object
            text: Post text content
            index: Post index for ID generation

        Returns:
            FacebookPost object or None
        """
        try:
            # Clean the text
            cleaned_text = self._clean_post_content(text)

            if len(cleaned_text) < 20:
                return None

            # Extract timestamp
            timestamp = self._extract_timestamp(text)

            # Generate post ID
            post_id = f"{user.name}_{index}_{hash(cleaned_text[:50])}_{int(time.time())}"

            return FacebookPost(
                user=user,
                post_id=post_id,
                content=cleaned_text,
                timestamp=timestamp,
                images=[],  # We'll handle images separately if needed
                post_url=user.profile_url
            )

        except Exception as e:
            logger.debug(f"Error creating post from text: {e}")
            return None

    def _looks_like_post_content(self, text: str) -> bool:
        """Check if text looks like a Facebook post"""
        text = text.strip().lower()

        # Skip if too short or too long
        if len(text) < 20 or len(text) > 5000:
            return False

        # Skip navigation/UI elements
        skip_patterns = [
            "home", "profile", "friends", "photos", "more", "about",
            "timeline", "posts", "reviews", "videos", "check-ins",
            "create post", "what's on your mind", "photo/video",
            "feeling/activity", "live video", "camera", "gif"
        ]

        if any(pattern in text for pattern in skip_patterns):
            return False

        # Look for post-like indicators
        post_indicators = [
            # Time indicators
            "hour", "minute", "day", "week", "month", "year", "ago",
            "yesterday", "today", "just now",
            # Engagement indicators
            "like", "comment", "share", "react",
            # Content indicators
            "feeling", "at", "with", "and", "others",
            # Common post words
            "just", "today", "yesterday", "amazing", "great", "love",
            "check out", "visited", "went to", "been to"
        ]

        indicator_count = sum(1 for indicator in post_indicators if indicator in text)

        # Must have at least 2 indicators and reasonable length
        return indicator_count >= 2 and 20 <= len(text) <= 2000

    async def _extract_post_info_detailed(self, element, user: FacebookUser, content: str) -> Optional[FacebookPost]:
        """Extract detailed post information from a post element"""
        try:
            # Clean the content
            cleaned_content = self._clean_post_content(content)

            if len(cleaned_content) < 20:  # Skip very short content
                return None

            # Extract timestamp (look for time indicators)
            timestamp = self._extract_timestamp(content)

            # Find images in the post
            images = []
            try:
                img_elements = await element.find(tag_name="img", find_all=True, timeout=2, raise_exc=False)
                for img in img_elements or []:
                    src = img.get_attribute("src")
                    if src and ("scontent" in src or "fbcdn" in src):  # Facebook image URLs
                        images.append(src)
            except:
                pass

            # Generate post ID
            post_id = f"{user.name}_{hash(cleaned_content[:100])}_{int(time.time())}"

            return FacebookPost(
                user=user,
                post_id=post_id,
                content=cleaned_content,
                timestamp=timestamp,
                images=images,
                post_url=user.profile_url
            )

        except Exception as e:
            logger.debug(f"Error extracting detailed post info: {e}")
            return None

    def _clean_post_content(self, content: str) -> str:
        """Clean and normalize post content"""
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content.strip())

        # Remove common UI elements
        ui_patterns = [
            r'\d+\s*(like|comment|share)s?',
            r'(like|comment|share)\s*\d*',
            r'see more',
            r'see less',
            r'view \d+ more comments?',
            r'write a comment',
            r'most relevant',
            r'top comments?',
        ]

        for pattern in ui_patterns:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)

        # Clean up extra spaces again
        content = re.sub(r'\s+', ' ', content.strip())

        return content

    def _extract_timestamp(self, content: str) -> str:
        """Extract timestamp from post content"""
        # Look for time patterns
        time_patterns = [
            r'(\d+)\s*(hour|hr|h)s?\s*ago',
            r'(\d+)\s*(minute|min|m)s?\s*ago',
            r'(\d+)\s*(day|d)s?\s*ago',
            r'(\d+)\s*(week|w)s?\s*ago',
            r'(\d+)\s*(month|mo)s?\s*ago',
            r'(\d+)\s*(year|yr|y)s?\s*ago',
            r'yesterday',
            r'just now',
            r'today'
        ]

        for pattern in time_patterns:
            match = re.search(pattern, content.lower())
            if match:
                return match.group(0)

        return "Unknown time"

    async def _is_post_element(self, element) -> bool:
        """Check if an element contains a post"""
        try:
            text_content = await element.text
            if not text_content or len(text_content) < 10:
                return False

            # Look for post indicators
            has_timestamp = bool(re.search(r'\d+[hmd]|\d+ (hour|minute|day|week|month)', text_content.lower()))
            has_content = len(text_content.split()) > 5

            return has_timestamp and has_content

        except:
            return False

    async def _extract_post_info(self, element, user: FacebookUser) -> Optional[FacebookPost]:
        """Extract post information from a post element"""
        try:
            # Get post text content
            text_content = await element.text
            if not text_content or len(text_content) < 10:
                return None

            # Extract timestamp
            timestamp = "Unknown"
            timestamp_match = re.search(r'(\d+[hmd]|\d+ (hour|minute|day|week|month))', text_content.lower())
            if timestamp_match:
                timestamp = timestamp_match.group(1)

            # Extract main content (remove timestamp and other metadata)
            content = text_content
            for pattern in [r'\d+[hmd]', r'\d+ (hour|minute|day|week|month)', r'Like', r'Comment', r'Share']:
                content = re.sub(pattern, '', content, flags=re.IGNORECASE)
            content = content.strip()

            # Find images in the post
            images = []
            try:
                img_elements = await element.find(tag_name="img", find_all=True, timeout=2, raise_exc=False)
                for img in img_elements or []:
                    src = img.get_attribute("src")
                    if src and "scontent" in src:  # Facebook image URLs contain 'scontent'
                        images.append(src)
            except:
                pass

            # Generate post ID (simplified)
            post_id = f"{user.name}_{hash(content[:50])}_{int(time.time())}"

            return FacebookPost(
                user=user,
                post_id=post_id,
                content=content,
                timestamp=timestamp,
                images=images,
                post_url=user.profile_url
            )

        except Exception as e:
            logger.debug(f"Error extracting post info: {e}")
            return None

    async def analyze_posts_for_travel(self, posts: List[FacebookPost]) -> List[tuple[FacebookPost, TravelAnalysis]]:
        """
        Analyze posts for travel-related content using Gemini AI

        Args:
            posts: List of FacebookPost objects to analyze

        Returns:
            List of tuples containing (post, travel_analysis)
        """
        results = []

        for post in posts:
            try:
                logger.info(f"Analyzing post: {post.post_id}")

                # Analyze text content
                text_analysis = await self.gemini_analyzer.analyze_text_for_travel(post.content)

                # If post has images, analyze them too
                image_analyses = []
                for image_url in post.images:
                    try:
                        # Download image
                        image_data = await self._download_image(image_url)
                        if image_data:
                            image_analysis = await self.gemini_analyzer.analyze_image_for_travel(image_data)
                            image_analyses.append(image_analysis)
                    except Exception as e:
                        logger.warning(f"Failed to analyze image {image_url}: {e}")

                # Combine text and image analysis
                combined_analysis = self._combine_analyses(text_analysis, image_analyses)

                if combined_analysis.is_travel_related:
                    results.append((post, combined_analysis))
                    logger.info(f"Travel content detected in post {post.post_id} (confidence: {combined_analysis.confidence_score:.2f})")

            except Exception as e:
                logger.error(f"Error analyzing post {post.post_id}: {e}")
                continue

        return results

    async def _download_image(self, image_url: str) -> Optional[bytes]:
        """Download image from URL"""
        try:
            # Use browser to download image
            response = await self.tab.execute_script(f"""
                return fetch('{image_url}')
                    .then(response => response.arrayBuffer())
                    .then(buffer => Array.from(new Uint8Array(buffer)));
            """)

            if response and isinstance(response, list):
                return bytes(response)

        except Exception as e:
            logger.debug(f"Error downloading image {image_url}: {e}")

        return None

    def _combine_analyses(self, text_analysis: TravelAnalysis, image_analyses: List[TravelAnalysis]) -> TravelAnalysis:
        """Combine text and image analyses into a single result"""

        # Start with text analysis
        combined = TravelAnalysis(
            is_travel_related=text_analysis.is_travel_related,
            confidence_score=text_analysis.confidence_score,
            travel_keywords=text_analysis.travel_keywords.copy(),
            destinations_mentioned=text_analysis.destinations_mentioned.copy(),
            travel_type=text_analysis.travel_type,
            analysis_summary=text_analysis.analysis_summary
        )

        # Incorporate image analyses
        for img_analysis in image_analyses:
            if img_analysis.is_travel_related:
                combined.is_travel_related = True
                combined.confidence_score = max(combined.confidence_score, img_analysis.confidence_score)
                combined.travel_keywords.extend(img_analysis.travel_keywords)
                combined.destinations_mentioned.extend(img_analysis.destinations_mentioned)

                if not combined.travel_type and img_analysis.travel_type:
                    combined.travel_type = img_analysis.travel_type

                combined.analysis_summary += f" | Image: {img_analysis.analysis_summary}"

        # Remove duplicates
        combined.travel_keywords = list(set(combined.travel_keywords))
        combined.destinations_mentioned = list(set(combined.destinations_mentioned))

        return combined

    async def analyze_posts_for_criteria(self, posts: List[FacebookPost], criteria: str) -> List[tuple[FacebookPost, TravelAnalysis]]:
        """
        Analyze posts for specific criteria using Gemini AI

        Args:
            posts: List of FacebookPost objects to analyze
            criteria: What to look for (e.g., "travel to Malaysia", "vacation posts")

        Returns:
            List of tuples containing (post, analysis) for matching posts
        """
        results = []

        for i, post in enumerate(posts, 1):
            try:
                if not await self._check_control_flags():
                    break

                logger.info(f"🤖 Analyzing post {i}/{len(posts)} with Gemini AI")
                print(f"   🤖 Analyzing post {i}/{len(posts)}...")

                # Create custom prompt for the specific criteria
                analysis = await self.gemini_analyzer.analyze_text_for_custom_criteria(post.content, criteria)

                # If post has images, analyze them too
                if post.images:
                    print(f"   🖼️ Analyzing {len(post.images)} images...")
                    image_analyses = []
                    for image_url in post.images:
                        try:
                            # Download image
                            image_data = await self._download_image(image_url)
                            if image_data:
                                image_analysis = await self.gemini_analyzer.analyze_image_for_custom_criteria(image_data, criteria)
                                image_analyses.append(image_analysis)
                        except Exception as e:
                            logger.warning(f"Failed to analyze image {image_url}: {e}")

                    # Combine text and image analysis
                    analysis = self._combine_analyses(analysis, image_analyses)

                if analysis.is_travel_related:
                    results.append((post, analysis))
                    logger.info(f"✅ Match found! Confidence: {analysis.confidence_score:.2f}")
                    print(f"   ✅ Match found! Confidence: {analysis.confidence_score:.2f}")
                else:
                    print(f"   ❌ No match (confidence: {analysis.confidence_score:.2f})")

            except Exception as e:
                logger.error(f"Error analyzing post {i}: {e}")
                continue

        return results

    async def search_and_analyze_by_criteria(
        self,
        search_string: str,
        search_criteria: str,
        max_users: int = 10,
        max_posts_per_user: int = 10
    ) -> Dict[str, Any]:
        """
        Complete search and analysis workflow with custom criteria

        Args:
            search_string: Exact string to search for (e.g., "Smith", "John Smith", etc.)
            search_criteria: What to look for in posts (e.g., "travel to Malaysia", "vacation posts")
            max_users: Maximum number of users to analyze
            max_posts_per_user: Maximum posts per user to analyze

        Returns:
            Dictionary containing all results
        """
        results = {
            "search_params": {
                "search_string": search_string,
                "search_criteria": search_criteria,
                "max_users": max_users,
                "max_posts_per_user": max_posts_per_user
            },
            "users_found": [],
            "matching_posts": [],
            "summary": {
                "total_users": 0,
                "total_posts_analyzed": 0,
                "matching_posts_found": 0,
                "top_destinations": [],
                "analysis_types": {}
            }
        }

        try:
            # Search for users using exact search string
            logger.info(f"🔍 Searching for exact string: {search_string}")
            print(f"\n🔍 Searching for: '{search_string}'")

            users = await self.search_users_by_exact_string(search_string, max_users)
            results["users_found"] = [asdict(user) for user in users]
            results["summary"]["total_users"] = len(users)

            if not users:
                logger.warning("No users found")
                print("❌ No users found for that search")
                return results

            print(f"✅ Found {len(users)} users for '{search_string}'")

            # Ask user if they want to continue
            if not self.wait_for_user_input(f"\nFound {len(users)} users. Press Enter to start analyzing their posts, or 'q' to quit: "):
                return results

            # Analyze posts for each user
            all_matching_posts = []
            total_posts = 0

            for i, user in enumerate(users, 1):
                if not await self._check_control_flags():
                    break

                print(f"\n👤 User {i}/{len(users)}: {user.name}")
                print(f"🔗 Profile: {user.profile_url}")

                # Ask if user wants to analyze this profile
                if not self.wait_for_user_input(f"Analyze {user.name}'s posts? (Enter to continue, 'q' to quit, 's' to skip): "):
                    break

                logger.info(f"📱 Analyzing posts for user: {user.name}")
                posts = await self.get_user_posts(user, max_posts_per_user)
                total_posts += len(posts)

                if posts:
                    print(f"📝 Found {len(posts)} posts. Analyzing with Gemini AI...")
                    matching_results = await self.analyze_posts_for_criteria(posts, search_criteria)
                    all_matching_posts.extend(matching_results)

                    if matching_results:
                        print(f"✅ Found {len(matching_results)} matching posts!")
                    else:
                        print("❌ No matching posts found")
                else:
                    print("❌ No posts found or profile not accessible")

            results["summary"]["total_posts_analyzed"] = total_posts
            results["summary"]["matching_posts_found"] = len(all_matching_posts)

            # Process matching posts
            destinations = []
            analysis_types = {}

            for post, analysis in all_matching_posts:
                post_data = asdict(post)
                post_data["analysis_result"] = asdict(analysis)
                results["matching_posts"].append(post_data)

                destinations.extend(analysis.destinations_mentioned)

                if analysis.travel_type:
                    analysis_types[analysis.travel_type] = analysis_types.get(analysis.travel_type, 0) + 1

            # Generate summary statistics
            from collections import Counter
            destination_counts = Counter(destinations)
            results["summary"]["top_destinations"] = [
                {"destination": dest, "mentions": count}
                for dest, count in destination_counts.most_common(10)
            ]
            results["summary"]["analysis_types"] = analysis_types

            logger.info(f"✅ Analysis complete: {len(all_matching_posts)} matching posts found")
            print(f"\n🎉 Analysis complete!")
            print(f"📊 Total posts analyzed: {total_posts}")
            print(f"✅ Matching posts found: {len(all_matching_posts)}")

            return results

        except Exception as e:
            logger.error(f"Error in search and analysis: {e}")
            results["error"] = str(e)
            return results


async def main():
    """
    Main execution function with improved workflow
    """
    # Configuration
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

    if not GEMINI_API_KEY:
        print("❌ Please set the GEMINI_API_KEY environment variable")
        print("Get your API key from: https://aistudio.google.com/app/apikey")
        return

    print("🚀 Facebook Tour Scraper with Gemini AI")
    print("=" * 50)

    # Search parameters
    print("Enter the EXACT string you want to search for on Facebook:")
    print("Examples:")
    print("  - 'Smith' (to find people with last name Smith)")
    print("  - 'John Smith' (to find specific person)")
    print("  - 'Bhat' (to find people with last name Bhat)")

    search_string = input("\nExact search string: ").strip()
    if not search_string:
        print("❌ Search string is required!")
        return

    search_criteria = input("What are you looking for in their posts? (e.g., 'travel to Malaysia', 'vacation posts'): ").strip()
    if not search_criteria:
        search_criteria = "travel and vacation posts"

    max_users = int(input("Max users to analyze (default 5): ").strip() or "5")
    max_posts = int(input("Max posts per user (default 10): ").strip() or "10")

    print(f"\n🎯 Search Configuration:")
    print(f"   Search String: '{search_string}'")
    print(f"   Looking for: {search_criteria}")
    print(f"   Max Users: {max_users}")
    print(f"   Max Posts per User: {max_posts}")

    # Ask for login method
    print(f"\n🔐 Login Options:")
    print("1. Manual login (recommended - safer)")
    print("2. Automatic login (requires credentials in .env)")

    login_choice = input("Choose login method (1 or 2): ").strip()

    async with FacebookScraper(gemini_api_key=GEMINI_API_KEY, headless=False) as scraper:

        if login_choice == "2":
            # Automatic login
            FACEBOOK_EMAIL = os.getenv("FACEBOOK_EMAIL")
            FACEBOOK_PASSWORD = os.getenv("FACEBOOK_PASSWORD")

            if not all([FACEBOOK_EMAIL, FACEBOOK_PASSWORD]):
                print("❌ For automatic login, please set FACEBOOK_EMAIL and FACEBOOK_PASSWORD in .env")
                return

            print("🔐 Attempting automatic login...")
            login_success = await scraper.login_to_facebook_auto(FACEBOOK_EMAIL, FACEBOOK_PASSWORD)
        else:
            # Manual login (default)
            print("🔐 Starting manual login process...")
            login_success = await scraper.login_to_facebook_manual(timeout=600)

        if not login_success:
            print("❌ Login failed!")
            return

        print("✅ Login successful!")

        # Wait for user to be ready
        if not scraper.wait_for_user_input("\n🚀 Ready to start scraping? Press Enter to continue or 'q' to quit: "):
            return

        # Perform search and analysis
        results = await scraper.search_and_analyze_by_criteria(
            search_string=search_string,
            search_criteria=search_criteria,
            max_users=max_users,
            max_posts_per_user=max_posts
        )

        # Save results to file
        timestamp = int(time.time())
        safe_criteria = "".join(c for c in search_criteria if c.isalnum() or c in (' ', '-', '_')).rstrip()[:30]
        safe_search_string = "".join(c for c in search_string if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
        output_file = f"facebook_analysis_{safe_search_string.replace(' ', '_')}_{safe_criteria.replace(' ', '_')}_{timestamp}.json"

        # Create results directory if it doesn't exist
        os.makedirs("results", exist_ok=True)
        output_path = os.path.join("results", output_file)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Results saved to: {output_path}")

        # Also save as CSV
        csv_file = output_file.replace('.json', '.csv')
        csv_path = os.path.join("results", csv_file)
        save_results_to_csv(results, csv_path)
        print(f"💾 CSV results saved to: {csv_path}")

        # Print summary
        summary = results["summary"]
        print(f"\n" + "="*50)
        print("📊 FINAL ANALYSIS SUMMARY")
        print("="*50)
        print(f"👥 Users found: {summary['total_users']}")
        print(f"📝 Posts analyzed: {summary['total_posts_analyzed']}")
        print(f"✅ Matching posts found: {summary['matching_posts_found']}")

        if summary["top_destinations"]:
            print(f"\n🌍 Top destinations mentioned:")
            for dest in summary["top_destinations"][:5]:
                print(f"   • {dest['destination']}: {dest['mentions']} mentions")

        if summary["analysis_types"]:
            print(f"\n🎯 Analysis types found:")
            for analysis_type, count in summary["analysis_types"].items():
                print(f"   • {analysis_type}: {count} posts")

        # Print some example matching posts
        if results["matching_posts"]:
            print(f"\n📋 Example matching posts:")
            for i, post_data in enumerate(results["matching_posts"][:3]):
                print(f"\n   Post {i+1}:")
                print(f"   👤 User: {post_data['user']['name']}")
                print(f"   💬 Content: {post_data['content'][:200]}...")
                print(f"   🎯 Confidence: {post_data['analysis_result']['confidence_score']:.2f}")
                print(f"   📝 Analysis: {post_data['analysis_result']['analysis_summary']}")
                destinations = post_data['analysis_result']['destinations_mentioned']
                if destinations:
                    print(f"   🌍 Destinations: {', '.join(destinations)}")

        print(f"\n🎉 Analysis complete! Check the results folder for detailed output.")

    search_criteria = input("What are you looking for? (e.g., 'travel to Malaysia', 'vacation posts', 'tour experiences'): ").strip()
    if not search_criteria:
        search_criteria = "travel and vacation posts"

    max_users = int(input("Max users to analyze (default 5): ").strip() or "5")
    max_posts = int(input("Max posts per user (default 10): ").strip() or "10")

    print(f"\n🎯 Search Configuration:")
    print(f"   Last Name: {last_name}")
    print(f"   Looking for: {search_criteria}")
    print(f"   Max Users: {max_users}")
    print(f"   Max Posts per User: {max_posts}")

    # Ask for login method
    print(f"\n🔐 Login Options:")
    print("1. Manual login (recommended - safer)")
    print("2. Automatic login (requires credentials in .env)")

    login_choice = input("Choose login method (1 or 2): ").strip()

    async with FacebookScraper(gemini_api_key=GEMINI_API_KEY, headless=False) as scraper:

        if login_choice == "2":
            # Automatic login
            FACEBOOK_EMAIL = os.getenv("FACEBOOK_EMAIL")
            FACEBOOK_PASSWORD = os.getenv("FACEBOOK_PASSWORD")

            if not all([FACEBOOK_EMAIL, FACEBOOK_PASSWORD]):
                print("❌ For automatic login, please set FACEBOOK_EMAIL and FACEBOOK_PASSWORD in .env")
                return

            print("🔐 Attempting automatic login...")
            login_success = await scraper.login_to_facebook_auto(FACEBOOK_EMAIL, FACEBOOK_PASSWORD)
        else:
            # Manual login (default)
            print("🔐 Starting manual login process...")
            login_success = await scraper.login_to_facebook_manual(timeout=300)

        if not login_success:
            print("❌ Login failed!")
            return

        print("✅ Login successful!")

        # Wait for user to be ready
        if not scraper.wait_for_user_input("\n� Ready to start scraping? Press Enter to continue or 'q' to quit: "):
            return

        # Perform search and analysis
        results = await scraper.search_and_analyze_by_criteria(
            search_string=search_string,
            search_criteria=search_criteria,
            max_users=max_users,
            max_posts_per_user=max_posts
        )

        # Save results to file
        timestamp = int(time.time())
        safe_criteria = "".join(c for c in search_criteria if c.isalnum() or c in (' ', '-', '_')).rstrip()[:30]
        output_file = f"facebook_analysis_{last_name}_{safe_criteria.replace(' ', '_')}_{timestamp}.json"

        # Create results directory if it doesn't exist
        os.makedirs("results", exist_ok=True)
        output_path = os.path.join("results", output_file)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Results saved to: {output_path}")

        # Also save as CSV
        csv_file = output_file.replace('.json', '.csv')
        csv_path = os.path.join("results", csv_file)
        save_results_to_csv(results, csv_path)
        print(f"💾 CSV results saved to: {csv_path}")

        # Print summary
        summary = results["summary"]
        print(f"\n" + "="*50)
        print("📊 FINAL ANALYSIS SUMMARY")
        print("="*50)
        print(f"👥 Users found: {summary['total_users']}")
        print(f"📝 Posts analyzed: {summary['total_posts_analyzed']}")
        print(f"✅ Matching posts found: {summary['matching_posts_found']}")

        if summary["top_destinations"]:
            print(f"\n🌍 Top destinations mentioned:")
            for dest in summary["top_destinations"][:5]:
                print(f"   • {dest['destination']}: {dest['mentions']} mentions")

        if summary["analysis_types"]:
            print(f"\n🎯 Analysis types found:")
            for analysis_type, count in summary["analysis_types"].items():
                print(f"   • {analysis_type}: {count} posts")

        # Print some example matching posts
        if results["matching_posts"]:
            print(f"\n📋 Example matching posts:")
            for i, post_data in enumerate(results["matching_posts"][:3]):
                print(f"\n   Post {i+1}:")
                print(f"   👤 User: {post_data['user']['name']}")
                print(f"   💬 Content: {post_data['content'][:200]}...")
                print(f"   🎯 Confidence: {post_data['analysis_result']['confidence_score']:.2f}")
                print(f"   📝 Analysis: {post_data['analysis_result']['analysis_summary']}")
                destinations = post_data['analysis_result']['destinations_mentioned']
                if destinations:
                    print(f"   🌍 Destinations: {', '.join(destinations)}")

        print(f"\n🎉 Analysis complete! Check the results folder for detailed output.")


def save_results_to_csv(results: Dict[str, Any], filename: str):
    """
    Save travel analysis results to CSV format

    Args:
        results: Results dictionary from search_and_analyze
        filename: Output CSV filename
    """
    import csv

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'user_name', 'user_location', 'post_content', 'timestamp',
            'is_travel_related', 'confidence_score', 'travel_keywords',
            'destinations_mentioned', 'travel_type', 'analysis_summary'
        ]

        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for post_data in results.get("travel_posts", []):
            row = {
                'user_name': post_data['user']['name'],
                'user_location': post_data['user']['location'],
                'post_content': post_data['content'][:500],  # Limit content length
                'timestamp': post_data['timestamp'],
                'is_travel_related': post_data['travel_analysis']['is_travel_related'],
                'confidence_score': post_data['travel_analysis']['confidence_score'],
                'travel_keywords': ', '.join(post_data['travel_analysis']['travel_keywords']),
                'destinations_mentioned': ', '.join(post_data['travel_analysis']['destinations_mentioned']),
                'travel_type': post_data['travel_analysis']['travel_type'],
                'analysis_summary': post_data['travel_analysis']['analysis_summary']
            }
            writer.writerow(row)


if __name__ == "__main__":
    # Example usage
    print("Facebook Tour Scraper with Gemini AI Analysis")
    print("=" * 50)

    # Run the main function
    asyncio.run(main())
