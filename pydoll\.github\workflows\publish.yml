name: Publish to <PERSON>y<PERSON> (Poetry)

on: workflow_dispatch

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Install Poetry
        run: |
          python -m pip install --upgrade pip
          pip install poetry

      - name: Configure Poetry
        run: poetry config pypi-token.pypi ${{ secrets.PYPI_API_TOKEN }}

      - name: Install dependencies
        run: poetry install

      - name: Build package
        run: poetry build

      - name: Publish to PyPI
        run: poetry publish
