"""
Debug script to see exactly what's happening during Facebook search
"""

import asyncio
import os
from facebook_scraper import FacebookScraper


async def debug_facebook_search():
    """Debug the Facebook search process step by step"""
    
    # Get Gemini API key
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ Please set GEMINI_API_KEY environment variable")
        return
    
    print("🐛 Facebook Search Debug Mode")
    print("=" * 40)
    
    last_name = input("Enter last name to debug (e.g., <PERSON>): ").strip()
    if not last_name:
        last_name = "<PERSON>"
    
    try:
        async with FacebookScraper(gemini_api_key=gemini_api_key, headless=False) as scraper:
            print("✅ Browser started successfully")
            
            # Manual login
            print("🔐 Please login to Facebook manually...")
            login_success = await scraper.login_to_facebook_manual(timeout=300)
            
            if not login_success:
                print("❌ Login failed!")
                return
            
            print("✅ Login successful!")
            
            # Debug search process
            print(f"\n🐛 Starting debug search for: {last_name}")
            
            # Step 1: Go to Facebook main page
            print("📍 Step 1: Going to Facebook main page...")
            await scraper.tab.go_to("https://www.facebook.com")
            await asyncio.sleep(3)
            
            current_url = await scraper.tab.current_url
            print(f"   Current URL: {current_url}")
            
            # Step 2: Try to find search box
            print("📍 Step 2: Looking for search box...")
            
            search_selectors = [
                {"tag_name": "input", "placeholder": "Search Facebook"},
                {"tag_name": "input", "aria_label": "Search Facebook"},
                {"tag_name": "input", "type": "search"},
                {"tag_name": "input", "name": "q"}
            ]
            
            search_input = None
            for i, selector in enumerate(search_selectors):
                try:
                    search_input = await scraper.tab.find(timeout=3, raise_exc=False, **selector)
                    if search_input:
                        print(f"   ✅ Found search input with selector {i+1}: {selector}")
                        break
                    else:
                        print(f"   ❌ Selector {i+1} failed: {selector}")
                except Exception as e:
                    print(f"   ❌ Selector {i+1} error: {e}")
            
            if not search_input:
                print("   ❌ Could not find search input field")
                print("   🔄 Trying direct URL method...")
                
                search_query = f"people named {last_name}"
                search_url = f"https://www.facebook.com/search/people/?q={search_query.replace(' ', '%20')}"
                print(f"   📍 Going to: {search_url}")
                
                await scraper.tab.go_to(search_url)
                await asyncio.sleep(5)
                
                current_url = await scraper.tab.current_url
                print(f"   Current URL after direct search: {current_url}")
                
            else:
                # Step 3: Use search box
                print("📍 Step 3: Using search box...")
                
                try:
                    await search_input.click()
                    await asyncio.sleep(1)
                    
                    search_query = f"people named {last_name}"
                    print(f"   Typing: {search_query}")
                    await search_input.type_text(search_query, interval=0.1)
                    await asyncio.sleep(2)
                    
                    # Press Enter
                    print("   Pressing Enter...")
                    await scraper.tab.execute_script("""
                        var event = new KeyboardEvent('keydown', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true
                        });
                        arguments[0].dispatchEvent(event);
                    """, search_input)
                    
                    await asyncio.sleep(5)
                    
                    current_url = await scraper.tab.current_url
                    print(f"   Current URL after search: {current_url}")
                    
                except Exception as e:
                    print(f"   ❌ Error using search box: {e}")
            
            # Step 4: Check what page we're on
            print("📍 Step 4: Analyzing current page...")
            
            current_url = await scraper.tab.current_url
            page_title = await scraper.tab.execute_script("return document.title;")
            
            print(f"   URL: {current_url}")
            print(f"   Title: {page_title}")
            
            if "search" in current_url:
                print("   ✅ We're on a search results page")
                
                # Step 5: Look for results
                print("📍 Step 5: Looking for search results...")
                
                # Get page text to see what's there
                page_text = await scraper.tab.execute_script("return document.body.innerText;")
                
                # Look for names in the page
                import re
                name_patterns = re.findall(r'([A-Z][a-z]+ [A-Z][a-z]+)', page_text)
                matching_names = [name for name in name_patterns if last_name.lower() in name.lower()]
                
                print(f"   Found {len(name_patterns)} total name patterns")
                print(f"   Found {len(matching_names)} matching names:")
                
                for name in matching_names[:10]:  # Show first 10
                    print(f"      - {name}")
                
                # Look for profile links
                all_links = await scraper.tab.find(tag_name="a", find_all=True, timeout=10)
                print(f"   Found {len(all_links)} total links")
                
                profile_links = []
                for link in all_links:
                    try:
                        href = link.get_attribute("href")
                        if href and scraper._is_valid_profile_url(href):
                            link_text = await link.text
                            if link_text and last_name.lower() in link_text.lower():
                                profile_links.append((link_text, href))
                    except:
                        continue
                
                print(f"   Found {len(profile_links)} potential profile links:")
                for text, url in profile_links[:5]:  # Show first 5
                    print(f"      - {text}: {url}")
                
            else:
                print("   ❌ Not on search results page")
                print("   🔍 Let's see what page we're on...")
                
                # Take a screenshot or get more info
                page_text_sample = await scraper.tab.execute_script(
                    "return document.body.innerText.substring(0, 500);"
                )
                print(f"   Page content sample: {page_text_sample}")
            
            print("\n🐛 Debug complete!")
            input("Press Enter to close browser...")
                
    except Exception as e:
        print(f"❌ Debug failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    asyncio.run(debug_facebook_search())
