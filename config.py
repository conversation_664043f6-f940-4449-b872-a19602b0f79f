"""
Configuration settings for Facebook Tour Scraper
"""

import os
from dataclasses import dataclass


@dataclass
class ScrapingConfig:
    """Configuration for scraping parameters"""

    # Facebook credentials (optional - for automatic login only)
    facebook_email: str = ""
    facebook_password: str = ""

    # Gemini API
    gemini_api_key: str = ""
    
    # Search parameters
    location: str = "Malaysia"
    last_name: str = "Smith"
    max_users: int = 10
    max_posts_per_user: int = 10
    
    # Browser settings
    headless: bool = False
    browser_timeout: int = 30
    
    # Output settings
    output_format: str = "json"  # json, csv, both
    output_directory: str = "results"
    
    # Rate limiting
    delay_between_users: float = 2.0
    delay_between_posts: float = 1.0
    
    @classmethod
    def from_env(cls) -> 'ScrapingConfig':
        """Create configuration from environment variables"""
        return cls(
            facebook_email=os.getenv("FACEBOOK_EMAIL", ""),
            facebook_password=os.getenv("FACEBOOK_PASSWORD", ""),
            gemini_api_key=os.getenv("GEMINI_API_KEY", ""),
            location=os.getenv("SEARCH_LOCATION", "Malaysia"),
            last_name=os.getenv("SEARCH_LAST_NAME", "Smith"),
            max_users=int(os.getenv("MAX_USERS", "10")),
            max_posts_per_user=int(os.getenv("MAX_POSTS_PER_USER", "10")),
            headless=os.getenv("HEADLESS", "false").lower() == "true",
            output_format=os.getenv("OUTPUT_FORMAT", "json"),
            output_directory=os.getenv("OUTPUT_DIR", "results")
        )
    
    def validate(self) -> bool:
        """Validate configuration - only Gemini API key is required"""
        return bool(self.gemini_api_key.strip())

    def validate_auto_login(self) -> bool:
        """Validate configuration for automatic login"""
        required_fields = [
            self.facebook_email,
            self.facebook_password,
            self.gemini_api_key
        ]

        return all(field.strip() for field in required_fields)


# Travel analysis keywords for enhanced detection
TRAVEL_KEYWORDS = [
    # Transportation
    "flight", "airplane", "airport", "train", "bus", "taxi", "uber", "lyft",
    "cruise", "ship", "boat", "ferry", "subway", "metro",
    
    # Accommodation
    "hotel", "resort", "hostel", "airbnb", "motel", "inn", "lodge", "villa",
    "apartment", "accommodation", "booking", "reservation",
    
    # Activities
    "vacation", "holiday", "trip", "travel", "tour", "sightseeing", "visit",
    "explore", "adventure", "excursion", "journey", "getaway", "backpack",
    
    # Places
    "beach", "mountain", "city", "country", "island", "desert", "forest",
    "museum", "monument", "landmark", "attraction", "destination",
    
    # Travel emotions
    "wanderlust", "explore", "discover", "adventure", "escape", "relax",
    "experience", "culture", "local", "authentic", "exotic"
]

# Common travel destinations
TRAVEL_DESTINATIONS = [
    # Asia
    "malaysia", "singapore", "thailand", "indonesia", "philippines", "vietnam",
    "japan", "korea", "china", "india", "nepal", "sri lanka", "maldives",
    
    # Europe
    "france", "italy", "spain", "germany", "uk", "england", "scotland",
    "ireland", "netherlands", "belgium", "switzerland", "austria", "greece",
    
    # Americas
    "usa", "canada", "mexico", "brazil", "argentina", "peru", "chile",
    "colombia", "costa rica", "panama",
    
    # Africa
    "south africa", "kenya", "tanzania", "morocco", "egypt", "madagascar",
    
    # Oceania
    "australia", "new zealand", "fiji", "tahiti",
    
    # Cities
    "paris", "london", "tokyo", "new york", "los angeles", "sydney",
    "bangkok", "singapore", "kuala lumpur", "hong kong", "dubai"
]
