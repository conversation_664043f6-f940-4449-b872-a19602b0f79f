"""
Simple Facebook Scraper - Searches for exact string and analyzes posts
"""

import asyncio
import os
import time
import json
from google import genai
from google.genai import types
from pydoll.browser import Chrome
from pydoll.browser.options import ChromiumOptions


class SimpleFacebookScraper:
    """Simple Facebook scraper that searches for exact strings"""
    
    def __init__(self, gemini_api_key: str):
        self.gemini_api_key = gemini_api_key
        self.browser = None
        self.tab = None
        self.logged_in = False
        
        # Initialize Gemini
        self.gemini_client = genai.Client(api_key=gemini_api_key)
        self.gemini_model = "gemini-2.5-flash-preview-04-17"
    
    async def __aenter__(self):
        await self.start_browser()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close_browser()
    
    async def start_browser(self):
        """Start the browser"""
        options = ChromiumOptions()
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--window-size=1920,1080')
        
        self.browser = Chrome(options=options)
        self.tab = await self.browser.start(headless=False)
        print("✅ Browser started")
    
    async def close_browser(self):
        """Close the browser"""
        if self.browser:
            try:
                await self.browser.stop()
                print("✅ Browser closed")
            except:
                pass
    
    async def manual_login(self, timeout: int = 600) -> bool:
        """Manual login to Facebook"""
        try:
            print("🔐 Opening Facebook login page...")
            await self.tab.go_to("https://www.facebook.com/login")
            
            print("\n" + "="*60)
            print("🔐 MANUAL LOGIN REQUIRED")
            print("="*60)
            print("Please login to Facebook manually in the browser.")
            print("The scraper will detect when you're logged in.")
            print("="*60)
            
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    current_url = await self.tab.current_url
                    
                    # Check if logged in
                    if ("facebook.com" in current_url and 
                        "login" not in current_url and 
                        "checkpoint" not in current_url):
                        
                        # Additional verification
                        try:
                            page_title = await self.tab.execute_script("return document.title;")
                            if "Facebook" in page_title and "Log In" not in page_title:
                                self.logged_in = True
                                print("\n✅ Login detected successfully!")
                                return True
                        except:
                            pass
                    
                    # Show progress every 10 seconds
                    elapsed = int(time.time() - start_time)
                    if elapsed % 10 == 0:
                        remaining = timeout - elapsed
                        print(f"\r⏳ Waiting for login... {elapsed}s elapsed, {remaining}s remaining", end="", flush=True)
                    
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    await asyncio.sleep(2)
            
            print(f"\n❌ Login timeout after {timeout} seconds")
            return False
            
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    async def search_exact_string(self, search_string: str) -> bool:
        """Search for exact string on Facebook"""
        try:
            if not self.logged_in:
                print("❌ Must be logged in first")
                return False
            
            print(f"🔍 Searching for: '{search_string}'")
            
            # Go to Facebook main page
            await self.tab.go_to("https://www.facebook.com")
            await asyncio.sleep(3)
            
            # Find search box
            search_input = None
            search_selectors = [
                {"tag_name": "input", "placeholder": "Search Facebook"},
                {"tag_name": "input", "aria_label": "Search Facebook"},
                {"tag_name": "input", "type": "search"}
            ]
            
            for selector in search_selectors:
                try:
                    search_input = await self.tab.find(timeout=3, raise_exc=False, **selector)
                    if search_input:
                        break
                except:
                    continue
            
            if not search_input:
                print("❌ Could not find search box")
                return False
            
            # Clear and type search string
            await search_input.click()
            await asyncio.sleep(1)
            
            await self.tab.execute_script("arguments[0].value = '';", search_input)
            await search_input.type_text(search_string, interval=0.1)
            await asyncio.sleep(2)
            
            # Press Enter
            await self.tab.execute_script("""
                var event = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    keyCode: 13,
                    bubbles: true
                });
                arguments[0].dispatchEvent(event);
            """, search_input)
            
            await asyncio.sleep(5)
            
            # Check if on search results
            current_url = await self.tab.current_url
            if "search" in current_url:
                print("✅ Search successful - on results page")
                return True
            else:
                print(f"❌ Search failed - current URL: {current_url}")
                return False
                
        except Exception as e:
            print(f"❌ Search error: {e}")
            return False
    
    async def get_search_results(self) -> list:
        """Get search results from current page"""
        try:
            print("📄 Analyzing search results...")
            
            # Scroll to load more results
            for i in range(3):
                await self.tab.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                await asyncio.sleep(2)
                print(f"   Scrolled {i+1}/3 times")
            
            # Get page text
            page_text = await self.tab.execute_script("return document.body.innerText;")
            
            # Get all links
            all_links = await self.tab.find(tag_name="a", find_all=True, timeout=10)
            
            results = []
            processed_urls = set()
            
            print(f"📊 Found {len(all_links)} links to analyze")
            
            for link in all_links:
                try:
                    href = link.get_attribute("href")
                    if not href or href in processed_urls:
                        continue
                    
                    processed_urls.add(href)
                    
                    # Check if it's a profile URL
                    if self._is_profile_url(href):
                        link_text = await link.text
                        if link_text and link_text.strip():
                            results.append({
                                'name': link_text.strip(),
                                'url': href
                            })
                            print(f"   👤 Found: {link_text.strip()}")
                
                except:
                    continue
            
            print(f"✅ Found {len(results)} potential profiles")
            return results
            
        except Exception as e:
            print(f"❌ Error getting results: {e}")
            return []
    
    def _is_profile_url(self, url: str) -> bool:
        """Check if URL is a Facebook profile"""
        if not url or "facebook.com" not in url:
            return False
        
        # Skip non-profile URLs
        skip_patterns = [
            "/pages/", "/groups/", "/events/", "/marketplace/", 
            "/watch/", "/gaming/", "/search/", "/help/"
        ]
        
        if any(pattern in url for pattern in skip_patterns):
            return False
        
        # Valid profile patterns
        if "/profile.php?id=" in url:
            return True
        
        # Username profiles
        if "facebook.com/" in url:
            parts = url.split("facebook.com/")
            if len(parts) > 1:
                username = parts[1].split("/")[0].split("?")[0]
                if username and not username.startswith(("www", "m", "mobile")):
                    return True
        
        return False
    
    async def analyze_with_gemini(self, text: str, criteria: str) -> dict:
        """Analyze text with Gemini AI"""
        try:
            prompt = f"""
            Analyze this social media post to see if it matches the criteria: "{criteria}"
            
            Post text: "{text}"
            
            Respond with JSON:
            {{
                "matches": true/false,
                "confidence": 0.0-1.0,
                "keywords": ["list", "of", "keywords"],
                "summary": "brief explanation"
            }}
            """
            
            contents = [
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=prompt)]
                )
            ]
            
            config = types.GenerateContentConfig(
                response_mime_type="application/json",
            )
            
            response = await asyncio.to_thread(
                self.gemini_client.models.generate_content,
                model=self.gemini_model,
                contents=contents,
                config=config
            )
            
            return json.loads(response.text)
            
        except Exception as e:
            print(f"❌ Gemini analysis error: {e}")
            return {
                "matches": False,
                "confidence": 0.0,
                "keywords": [],
                "summary": f"Analysis failed: {e}"
            }


async def main():
    """Main function"""
    # Get API key
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ Please set GEMINI_API_KEY environment variable")
        return
    
    print("🚀 Simple Facebook Scraper")
    print("=" * 40)
    
    # Get search parameters
    search_string = input("Enter EXACT string to search for: ").strip()
    if not search_string:
        print("❌ Search string required!")
        return
    
    criteria = input("What to look for in posts: ").strip()
    if not criteria:
        criteria = "travel posts"
    
    print(f"\n🎯 Configuration:")
    print(f"   Search: '{search_string}'")
    print(f"   Criteria: '{criteria}'")
    
    # Start scraping
    async with SimpleFacebookScraper(gemini_api_key) as scraper:
        # Manual login
        login_success = await scraper.manual_login()
        if not login_success:
            print("❌ Login failed!")
            return
        
        # Search
        search_success = await scraper.search_exact_string(search_string)
        if not search_success:
            print("❌ Search failed!")
            return
        
        # Get results
        results = await scraper.get_search_results()
        if not results:
            print("❌ No results found!")
            return
        
        print(f"\n✅ Found {len(results)} profiles")
        for i, result in enumerate(results, 1):
            print(f"   {i}. {result['name']}")
        
        # Save results
        timestamp = int(time.time())
        output_file = f"facebook_search_results_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'search_string': search_string,
                'criteria': criteria,
                'results': results,
                'timestamp': timestamp
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to: {output_file}")
        print("🎉 Search complete!")


if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    asyncio.run(main())
