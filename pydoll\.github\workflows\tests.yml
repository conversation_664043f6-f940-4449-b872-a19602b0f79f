name: PyDoll Tests Suite

on: 
  push:
  pull_request:
    
jobs:
  tests:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.11", "3.12", "3.13"]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install poetry
          poetry install
      - name: Run tests with coverage
        run: |
          poetry run pytest -s -x --cov=pydoll -vv --cov-report=xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v5
        with:
          file: ./coverage.xml
          flags: tests
          name: PyDoll Tests
          fail_ci_if_error: true
          token: ${{ secrets.CODECOV_TOKEN }}
