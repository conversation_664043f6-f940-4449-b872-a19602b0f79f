"""
Test script for direct profile analysis
"""

import asyncio
import os
from facebook_scraper_v2 import FacebookScraperV2


async def test_direct_analysis():
    """Test direct profile analysis"""
    
    # Get API key
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ Please set GEMINI_API_KEY environment variable")
        return
    
    print("🧪 Testing Direct Profile Analysis")
    print("=" * 40)
    
    # Get test profile URL
    profile_url = input("Enter a Facebook profile URL to test: ").strip()
    if not profile_url:
        print("❌ Profile URL required!")
        return
    
    criteria = input("What to look for (e.g., 'travel posts'): ").strip()
    if not criteria:
        criteria = "travel posts"
    
    print(f"\n🎯 Testing Analysis:")
    print(f"   Profile: {profile_url}")
    print(f"   Criteria: {criteria}")
    
    async with FacebookScraperV2(gemini_api_key) as scraper:
        # Login
        print("\n🔐 Please login manually...")
        login_success = await scraper.manual_login(timeout=300)
        if not login_success:
            print("❌ Login failed!")
            return
        
        # Visit profile
        print(f"\n📱 Visiting profile...")
        success = await scraper.visit_profile_directly(profile_url)
        if not success:
            print("❌ Failed to load profile!")
            return
        
        print("✅ Profile loaded successfully!")
        
        # Test screenshot analysis
        print("\n📸 Testing screenshot analysis...")
        screenshot = await scraper.take_screenshot()
        
        if screenshot:
            print("✅ Screenshot captured")
            
            print("🤖 Analyzing with Gemini...")
            analysis = await scraper.analyze_screenshot_with_gemini(screenshot, criteria)
            
            print(f"\n📊 Screenshot Analysis Results:")
            print(f"   Matches: {analysis.get('matches', False)}")
            print(f"   Confidence: {analysis.get('confidence', 0.0):.2f}")
            print(f"   Findings: {analysis.get('findings', [])}")
            print(f"   Analysis: {analysis.get('analysis', 'No analysis')}")
        else:
            print("❌ Failed to capture screenshot")
        
        # Test content scraping
        test_scraping = input("\nTest content scraping too? (y/n): ").strip().lower()
        if test_scraping == 'y':
            print("\n📜 Testing content scraping...")
            content = await scraper.scrape_posts_text_and_images()
            
            print(f"📊 Scraped Content:")
            print(f"   Text length: {len(content.get('text_content', ''))} characters")
            print(f"   Images found: {len(content.get('images', []))}")
            
            if content.get('text_content'):
                print(f"   Text preview: {content['text_content'][:200]}...")
            
            if content.get('images'):
                print(f"   Image URLs:")
                for i, img in enumerate(content['images'][:3]):
                    print(f"     {i+1}. {img.get('url', 'No URL')}")
            
            # Analyze scraped content
            print("\n🤖 Analyzing scraped content...")
            content_analysis = await scraper.analyze_scraped_content(content, criteria)
            
            print(f"\n📊 Content Analysis Results:")
            print(f"   Overall Match: {content_analysis.get('overall_match', False)}")
            print(f"   Combined Confidence: {content_analysis.get('combined_confidence', 0.0):.2f}")
            print(f"   Summary: {content_analysis.get('summary', 'No summary')}")
            
            # Text analysis details
            text_analysis = content_analysis.get('text_analysis', {})
            if text_analysis:
                print(f"\n   Text Analysis:")
                print(f"     Matches: {text_analysis.get('matches', False)}")
                print(f"     Confidence: {text_analysis.get('confidence', 0.0):.2f}")
                print(f"     Keywords: {text_analysis.get('keywords', [])}")
            
            # Image analysis details
            image_analyses = content_analysis.get('image_analyses', [])
            if image_analyses:
                print(f"\n   Image Analyses ({len(image_analyses)} images):")
                for i, img_analysis in enumerate(image_analyses):
                    print(f"     Image {i+1}:")
                    print(f"       Matches: {img_analysis.get('matches', False)}")
                    print(f"       Confidence: {img_analysis.get('confidence', 0.0):.2f}")
                    print(f"       Description: {img_analysis.get('description', 'No description')}")
        
        print("\n🎉 Test complete!")


if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    asyncio.run(test_direct_analysis())
