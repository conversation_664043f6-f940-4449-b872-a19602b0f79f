"""
Installation script for Facebook Tour Scraper
"""

import os
import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("   Requires Python 3.8 or higher")
        return False


def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    # Check if requirements.txt exists
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    # Install dependencies
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install -r requirements.txt", "Installing dependencies")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True


def setup_environment():
    """Setup environment configuration"""
    print("⚙️  Setting up environment...")
    
    env_file = Path(".env")
    env_template = Path(".env.template")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_template.exists():
        # Copy template to .env
        with open(env_template, 'r') as template:
            content = template.read()
        
        with open(env_file, 'w') as env:
            env.write(content)
        
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file with your actual credentials")
        return True
    else:
        print("❌ .env.template not found")
        return False


def create_output_directory():
    """Create output directory for results"""
    print("📁 Creating output directory...")
    
    output_dir = Path("results")
    output_dir.mkdir(exist_ok=True)
    
    print("✅ Output directory created")
    return True


def verify_installation():
    """Verify the installation"""
    print("🧪 Verifying installation...")
    
    try:
        # Test imports
        import pydoll
        import google.genai
        import aiofiles
        
        print("✅ All dependencies imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "=" * 60)
    print("🎉 INSTALLATION COMPLETE!")
    print("=" * 60)
    print("\n📋 Next Steps:")
    print("1. Edit the .env file with your credentials:")
    print("   - FACEBOOK_EMAIL")
    print("   - FACEBOOK_PASSWORD")
    print("   - GEMINI_API_KEY")
    print("\n2. Get your Gemini API key:")
    print("   - Visit: https://aistudio.google.com/app/apikey")
    print("   - Create a new API key")
    print("   - Add it to your .env file")
    print("\n3. Test your setup:")
    print("   python test_setup.py")
    print("\n4. Run the scraper:")
    print("   python example_usage.py")
    print("\n📖 For more information, see README.md")


def main():
    """Main installation function"""
    print("🚀 Facebook Tour Scraper - Installation")
    print("=" * 50)
    
    steps = [
        ("Python Version Check", check_python_version),
        ("Dependency Installation", install_dependencies),
        ("Environment Setup", setup_environment),
        ("Output Directory", create_output_directory),
        ("Installation Verification", verify_installation)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ {step_name} failed with error: {e}")
            failed_steps.append(step_name)
    
    print("\n" + "=" * 50)
    print("📊 INSTALLATION SUMMARY")
    print("=" * 50)
    
    if failed_steps:
        print(f"❌ Failed steps: {', '.join(failed_steps)}")
        print("\nPlease fix the issues above and run the installer again.")
        sys.exit(1)
    else:
        print("✅ All installation steps completed successfully!")
        print_next_steps()


if __name__ == "__main__":
    main()
