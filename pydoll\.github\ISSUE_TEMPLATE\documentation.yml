name: Documentation Issue
description: Report missing, incorrect, or unclear documentation
title: "[Docs]: "
labels: ["documentation", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        # pydoll Documentation Issue
        
        Thank you for helping us improve the documentation. This form will guide you through providing the information needed to address documentation issues effectively.
  
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist before reporting
      description: Please make sure you've completed the following steps before submitting a documentation issue.
      options:
        - label: I have searched for [similar documentation issues](https://github.com/thalissonvs/pydoll/issues) and didn't find a duplicate.
          required: true
        - label: I have checked the latest documentation to verify this issue still exists.
          required: true
  
  - type: dropdown
    id: type
    attributes:
      label: Type of Documentation Issue
      description: What type of documentation issue are you reporting?
      options:
        - Missing documentation (information does not exist)
        - Incorrect documentation (information is wrong)
        - Unclear documentation (information is confusing or ambiguous)
        - Outdated documentation (information is no longer valid)
        - Other (please specify in description)
    validations:
      required: true
  
  - type: input
    id: location
    attributes:
      label: Documentation Location
      description: Where is the documentation with issues located? Provide URLs, file paths, or section names.
      placeholder: e.g., https://docs.example.com/pydoll/api.html#section, README.md, API Reference for Client class
    validations:
      required: true
  
  - type: textarea
    id: description
    attributes:
      label: Issue Description
      description: Describe the issue with the documentation in detail.
      placeholder: |
        The documentation for the `Client.connect()` method doesn't mention the timeout parameter, 
        which I discovered by looking at the source code.
    validations:
      required: true
  
  - type: textarea
    id: suggested_fix
    attributes:
      label: Suggested Fix
      description: If you have a suggestion for how to fix the documentation, please provide it here.
      placeholder: |
        Add the following to the `Client.connect()` documentation:
        
        ```
        Parameters:
          timeout (float, optional): Connection timeout in seconds. Defaults to 30.
        ```
  
  - type: textarea
    id: additional_info
    attributes:
      label: Additional Information
      description: Any additional context or information that might help address this documentation issue.
      placeholder: |
        I found this issue when trying to implement a connection with a shorter timeout for my specific use case.
  
  - type: dropdown
    id: contribution
    attributes:
      label: Contribution
      description: Would you be willing to contribute a fix for this documentation?
      options:
        - Yes, I'd be willing to submit a PR with the fix
        - No, I don't have the capacity to fix this
    validations:
      required: true 