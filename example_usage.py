"""
Example usage of Facebook Tour Scraper

This script demonstrates how to use the Facebook scraper to find users
and analyze their posts for travel content.
"""

import asyncio
import json
import os
from pathlib import Path

from facebook_scraper import FacebookScraper, save_results_to_csv
from config import ScrapingConfig


async def basic_example():
    """Basic usage example"""
    
    # Load configuration
    config = ScrapingConfig.from_env()
    
    if not config.validate():
        print("❌ Configuration validation failed!")
        print("Please set the following environment variables:")
        print("- FACEBOOK_EMAIL")
        print("- FACEBOOK_PASSWORD") 
        print("- GEMINI_API_KEY")
        return
    
    print("🚀 Starting Facebook Tour Scraper")
    print(f"📍 Searching for '{config.last_name}' in '{config.location}'")
    
    async with FacebookScraper(gemini_api_key=config.gemini_api_key, headless=config.headless) as scraper:
        
        # Login
        print("🔐 Logging in to Facebook...")
        login_success = await scraper.login_to_facebook(config.facebook_email, config.facebook_password)
        
        if not login_success:
            print("❌ Login failed!")
            return
        
        print("✅ Login successful!")
        
        # Search and analyze
        print("🔍 Searching for users and analyzing posts...")
        results = await scraper.search_and_analyze(
            location=config.location,
            last_name=config.last_name,
            max_users=config.max_users,
            max_posts_per_user=config.max_posts_per_user
        )
        
        # Create output directory
        output_dir = Path(config.output_directory)
        output_dir.mkdir(exist_ok=True)
        
        # Save results
        timestamp = int(asyncio.get_event_loop().time())
        base_filename = f"travel_analysis_{config.location}_{config.last_name}_{timestamp}"
        
        if config.output_format in ["json", "both"]:
            json_file = output_dir / f"{base_filename}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"💾 JSON results saved to: {json_file}")
        
        if config.output_format in ["csv", "both"]:
            csv_file = output_dir / f"{base_filename}.csv"
            save_results_to_csv(results, str(csv_file))
            print(f"💾 CSV results saved to: {csv_file}")
        
        # Print summary
        print_summary(results)


async def advanced_example():
    """Advanced usage with custom parameters"""
    
    config = ScrapingConfig.from_env()
    
    # Custom search parameters
    searches = [
        {"location": "Bali", "last_name": "Johnson"},
        {"location": "Tokyo", "last_name": "Williams"},
        {"location": "Paris", "last_name": "Brown"}
    ]
    
    async with FacebookScraper(gemini_api_key=config.gemini_api_key) as scraper:
        
        # Login once
        await scraper.login_to_facebook(config.facebook_email, config.facebook_password)
        
        all_results = []
        
        for search in searches:
            print(f"🔍 Searching: {search['last_name']} in {search['location']}")
            
            results = await scraper.search_and_analyze(
                location=search["location"],
                last_name=search["last_name"],
                max_users=5,
                max_posts_per_user=5
            )
            
            results["search_id"] = f"{search['location']}_{search['last_name']}"
            all_results.append(results)
            
            # Add delay between searches
            await asyncio.sleep(5)
        
        # Save combined results
        output_file = f"combined_travel_analysis_{int(asyncio.get_event_loop().time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Combined results saved to: {output_file}")
        
        # Print combined summary
        print_combined_summary(all_results)


def print_summary(results):
    """Print analysis summary"""
    summary = results.get("summary", {})
    
    print("\n" + "="*50)
    print("📊 ANALYSIS SUMMARY")
    print("="*50)
    print(f"👥 Users found: {summary.get('total_users', 0)}")
    print(f"📝 Posts analyzed: {summary.get('total_posts_analyzed', 0)}")
    print(f"✈️  Travel posts found: {summary.get('travel_posts_found', 0)}")
    
    # Top destinations
    top_destinations = summary.get("top_destinations", [])
    if top_destinations:
        print(f"\n🌍 Top destinations mentioned:")
        for dest in top_destinations[:5]:
            print(f"   • {dest['destination']}: {dest['mentions']} mentions")
    
    # Travel types
    travel_types = summary.get("travel_types", {})
    if travel_types:
        print(f"\n🎯 Travel types found:")
        for travel_type, count in travel_types.items():
            print(f"   • {travel_type}: {count} posts")
    
    # Example posts
    travel_posts = results.get("travel_posts", [])
    if travel_posts:
        print(f"\n📋 Example travel posts:")
        for i, post in enumerate(travel_posts[:3]):
            print(f"\n   Post {i+1}:")
            print(f"   👤 User: {post['user']['name']}")
            print(f"   📍 Location: {post['user']['location']}")
            print(f"   💬 Content: {post['content'][:100]}...")
            print(f"   🎯 Confidence: {post['travel_analysis']['confidence_score']:.2f}")
            destinations = post['travel_analysis']['destinations_mentioned']
            if destinations:
                print(f"   🌍 Destinations: {', '.join(destinations)}")


def print_combined_summary(all_results):
    """Print summary for combined results"""
    total_users = sum(r.get("summary", {}).get("total_users", 0) for r in all_results)
    total_posts = sum(r.get("summary", {}).get("total_posts_analyzed", 0) for r in all_results)
    total_travel_posts = sum(r.get("summary", {}).get("travel_posts_found", 0) for r in all_results)
    
    print("\n" + "="*50)
    print("📊 COMBINED ANALYSIS SUMMARY")
    print("="*50)
    print(f"🔍 Searches performed: {len(all_results)}")
    print(f"👥 Total users found: {total_users}")
    print(f"📝 Total posts analyzed: {total_posts}")
    print(f"✈️  Total travel posts found: {total_travel_posts}")
    
    # Collect all destinations
    all_destinations = []
    for results in all_results:
        for post in results.get("travel_posts", []):
            all_destinations.extend(post['travel_analysis']['destinations_mentioned'])
    
    if all_destinations:
        from collections import Counter
        dest_counts = Counter(all_destinations)
        print(f"\n🌍 Most mentioned destinations across all searches:")
        for dest, count in dest_counts.most_common(10):
            print(f"   • {dest}: {count} mentions")


if __name__ == "__main__":
    print("Facebook Tour Scraper - Example Usage")
    print("="*50)
    
    # Choose which example to run
    example_type = input("Choose example (basic/advanced): ").strip().lower()
    
    if example_type == "advanced":
        asyncio.run(advanced_example())
    else:
        asyncio.run(basic_example())
