from enum import Enum


class RuntimeMethod(str, Enum):
    ADD_BINDING = 'Runtime.addBinding'
    AWAIT_PROMISE = 'Runtime.awaitPromise'
    CALL_FUNCTION_ON = 'Runtime.callFunctionOn'
    COMPILE_SCRIPT = 'Runtime.compileScript'
    DISABLE = 'Runtime.disable'
    DISCARD_CONSOLE_ENTRIES = 'Runtime.discardConsoleEntries'
    ENABLE = 'Runtime.enable'
    EVALUATE = 'Runtime.evaluate'
    GET_PROPERTIES = 'Runtime.getProperties'
    GLOBAL_LEXICAL_SCOPE_NAMES = 'Runtime.globalLexicalScopeNames'
    QUERY_OBJECTS = 'Runtime.queryObjects'
    RELEASE_OBJECT = 'Runtime.releaseObject'
    RELEASE_OBJECT_GROUP = 'Runtime.releaseObjectGroup'
    REMOVE_BINDING = 'Runtime.removeBinding'
    RUN_IF_WAITING_FOR_DEBUGGER = 'Runtime.runIfWaitingForDebugger'
    RUN_SCRIPT = 'Runtime.runScript'
    SET_ASYNC_CALL_STACK_DEPTH = 'Runtime.setAsyncCallStackDepth'
    GET_EXCEPTION_DETAILS = 'Runtime.getExceptionDetails'
    GET_HEAP_USAGE = 'Runtime.getHeapUsage'
    GET_ISOLATE_ID = 'Runtime.getIsolateId'
    SET_CUSTOM_OBJECT_FORMATTER_ENABLED = 'Runtime.setCustomObjectFormatterEnabled'
    SET_MAX_CALL_STACK_SIZE_TO_CAPTURE = 'Runtime.setMaxCallStackSizeToCapture'
    TERMINATE_EXECUTION = 'Runtime.terminateExecution'
