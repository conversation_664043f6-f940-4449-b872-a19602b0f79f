# Bug Fix Pull Request

## Related Issue(s)
<!-- Link the bug report that's being fixed by this PR. Use the format: "Fixes #123" or "Resolves #123" -->

## Bug Description
<!-- Briefly describe the bug that's being fixed -->

## Root Cause
<!-- Explain the root cause of the bug -->

## Solution
<!-- Describe your solution to fix the bug -->

## Verification Steps
<!-- List the steps to verify this fix works -->
1. 
2. 
3. 

## Code Example
<!-- If applicable, provide a code example demonstrating the fix -->
```python
# Example code showing the fix
```

## Before / After
<!-- If applicable, provide before/after screenshots or code snippets -->

## Testing
<!-- Describe the tests you added or modified to verify your fix -->

## Testing Checklist
- [ ] Added regression test that would have caught this bug
- [ ] Modified existing tests to account for this fix
- [ ] All tests pass
- [ ] Edge cases have been tested

## Impact
<!-- Describe any potential impact this fix might have on existing functionality -->
- [ ] Low (isolated fix with no side effects)
- [ ] Medium (might affect closely related functionality)
- [ ] High (affects multiple areas or changes core behavior)

## Backwards Compatibility
- [ ] This change is fully backward compatible
- [ ] This change introduces backward incompatibilities (explain below)

## Checklist before requesting a review
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my code
- [ ] I have added test cases that prove my fix is effective
- [ ] I have run `poetry run task lint` and fixed any issues
- [ ] I have run `poetry run task test` and all tests pass
- [ ] My commits follow the [conventional commits](https://www.conventionalcommits.org/) style with message explaining the fix 